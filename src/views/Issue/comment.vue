<template>
  <div class="comments">
    <van-sticky>
      <van-nav-bar
          :title="title"
          left-arrow
          @click-left="onClickLeft"
          @click-right="onClickRight"
      >
        <template #right>
          <van-icon name="plus" size="18" color="#1989fa" />
        </template>
      </van-nav-bar>
    </van-sticky>
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
      <van-list
        class="list"
        v-model:loading="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="onLoad">
        <div v-for="item in comments" :key="item.issue_talkid">
          <van-swipe-cell>
            <div class="item-wrapper">
              <div class="item-text" v-if="item.contenttype === 'text'">
                <div class="name-bg">
                  <span>{{item.realname ? item.realname.split('')[item.realname.length - 1] : '无'}}</span>
                </div>
                <div class="right-wrapper">
                  <div class="name">{{item.realname}}</div>
                  <div class="content">{{item.content}}</div>
                  <div class="time">
                    <span>{{item.createdate}}</span>
                    <van-icon v-if="item.userid === userId" name="delete-o" color="#616F7D" @click="deleteItem(item)" /> 
                  </div>
                </div>
              </div>
              <div class="item-text" v-else>
                <div class="name-bg">
                  <span>{{item.realname ? item.realname.split('')[item.realname.length - 1] : '无'}}</span>
                </div>
                <div class="right-wrapper">
                  <div class="name">{{item.realname}}</div>
                  <div class="img" @click="showImagePre(item)">
                    <van-image
                      class="comments-img"
                      :src="getImageUrl(item)"
                    />
                  </div>
                  <div class="time">
                    <span>{{item.createdate}}</span>
                    <van-icon v-if="item.userid === userId" name="delete-o" color="#616F7D" @click="deleteItem(item)" />
                  </div>
                </div>
              </div>
            </div>
          </van-swipe-cell>
        </div>
      </van-list>
    </van-pull-refresh>
  </div>
</template>

<script setup>
import router from "@/router";
import {onBeforeUpdate, onMounted, ref, watch} from "vue";
import {getCurrentDate} from "@/utils";
import api from "@/api";
import {getToken} from "@/utils/token";
import {getOrganizeId} from "@/utils/organizeId";
import {showImagePreview, showToast} from "vant";
import {useRoute} from "vue-router";
import {getUserInfo} from "@/utils/user";
import Preview from "@/views/Project/Preview.vue";
const issueId = ref('')
const token = ref('')
const organizeId = ref('')
const userId = ref('')
const title = ref('评论')
const comments = ref([])
const createUserId = ref('')
const refreshing = ref(false)
const finished = ref(false)
const loading = ref(false)
/**
 * 左侧点击回退上一级路由
 * 注：replace 方法不会增加路由信息
 */
function onClickLeft(){
  router.back()
}
/**
 * 进入新增评论
 */
function onClickRight() {
  router.push({
    path:'/addComment',
    query: {
      issueId: issueId.value
    }
  })
}
/**
 * 获取问题详情
 */
async function getIssueDetail() {
  const res = await api.GetIssueDetail({
    token: token.value,
    issueId: issueId.value
  })
  if (res.data.Ret === 1) {
    const issueDetail = res.data.Data
    comments.value = issueDetail.comments
    if (comments.value.length > 0) {
      title.value = `评论(${comments.value.length})`
    }else {
      title.value = '评论'
    }
    createUserId.value = issueDetail.issueObj.CreateUserId
  }
  finished.value = true
  loading.value = false
}
/**
 * 列表加载方法
 */
function onLoad(){
}
/**
 * 刷新列表
 */
function onRefresh() {
  // 清空列表数据
  finished.value = false;
  // 将 loading 设置为 true，表示处于加载状态
  loading.value = true;
  // 加载数据
  getIssueDetail()
  // 取消刷新状态
  refreshing.value = false
}
/**
 * 删除评论
 */
async function deleteItem(item) {
  const res = await api.RemoveComment({
    Token: token.value,
    IssueId: issueId.value,
    Issue_TalkId: item.issue_talkid
  })
  if (res.data.Ret === 1) {
    onRefresh()
  }else {
    showToast(res.data.Msg)
  }
}
/**
 * 预览图片
 */
function showImagePre(item) {
  const imgId = item.contenttypeJson.contentImgId
  const temp = [`${window.IP_CONFIG.BASE_URL}/api/v1/attach/preview?id=${imgId}&Token=${getToken()}`]
  showImagePreview(temp)
}
function getImageUrl(item) {
  const imgId = item.contenttypeJson.contentImgId
  return `${window.IP_CONFIG.BASE_URL}/api/v1/attach/preview?id=${imgId}&Token=${getToken()}`
}
onMounted(()=>{
  const route = useRoute()
  issueId.value = route.query.issueId
  organizeId.value = getOrganizeId()
  token.value = getToken()
  userId.value = JSON.parse(getUserInfo()).UserId
  getIssueDetail()
})
</script>
<style scoped lang="scss">
.comments{
  height: 100%;
  background-color: #f2f2f2;
  ::-webkit-scrollbar{
    display: none;
  }
  .list{
    padding: 16px;
    overflow-y: scroll;
    .item-wrapper{
      display: flex;
      margin-bottom: 16px;
      padding: 12px;
      border-radius: 4px;
      background-color: white;
      .item-text{
        width: 100%;
        display: flex;
        .name-bg{
          display: flex;
          justify-content: center;
          align-items: center;
          background-color: #283A4F;
          width: 32px;
          height: 32px;
          border-radius: 16px;
          span{
            color: #fff;
            font-size: 16px;
          }
        }
        .right-wrapper{
          flex: 1;
          margin-left: 16px;
          font-size: 16px;
          .name{
            font-weight: 500;
            color: #283A4F;
          }
          .content{
            margin-top: 12px;
            color: #283A4F;
          }
          .time{
            display: flex;
            text-align: center;
            justify-content: space-between;
            margin-top: 12px;
            color: #A6AEB6;
          }
          .img{
            width: 100%;
            height: 120px;
            margin-top: 12px;
            .comments-img{
              width: 100%;
              height: 120px;
            }
          }
        }
      }
    }
  }
}
</style>

const path = require('path')
// import postcsspxtoviewport from 'postcss-px-to-viewport-8-plugin'

const resolve = dir => path.join(__dirname, dir)
const isProduction = process.env.NODE_ENV === 'production'
const proxyIp = require('./src/config/proxyIP')
const proxy = {}
for (const key in proxyIp) {
    proxy[`/${key}`] = {
      target: proxyIp[key],
      changeOrigin: true,
      secure: true,
      pathRewrite: {
        [`^/${key}`]: ''
      }
    }
  }
// const { VantResolver } = require('@vant/auto-import-resolver');
// const ComponentsPlugin = require('unplugin-vue-components/webpack');
const setVersion = process.env.npm_package_config_buildVersion
const webpack = require('webpack')
const packageJson = require('./package.json')

module.exports = {
    publicPath: './',
    // 是否启用eslint
    lintOnSave: !isProduction,
    devServer: {
        open: true,
        host: '0.0.0.0',
        port: 8081,
    },
    configureWebpack: {
        devtool: 'source-map',
        output: {
            filename: `js/[name].${setVersion}.js`,
            chunkFilename: `js/[name].${setVersion}.js`
        },
        optimization: {
            runtimeChunk: 'single',
            splitChunks: {
                chunks: 'all',
                minSize: 20000,
                maxSize: 244000,
                cacheGroups: {
                    vendor: {
                        test: /[\\/]node_modules[\\/]/,
                        name: 'vendors',
                        chunks: 'all',
                    },
                },
            },
        },
        plugins: [
            new webpack.DefinePlugin({
                'process.env.VUE_APP_VERSION': JSON.stringify(packageJson.config.buildVersion)
            })
        ]
    },
    css: {
        extract: {
            filename: `css/[name].${setVersion}.css`,
            chunkFilename: `css/[name].${setVersion}.css`
        }
    }
};
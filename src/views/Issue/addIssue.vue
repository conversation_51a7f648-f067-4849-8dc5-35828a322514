<template>
  <div class="add-container">
    <van-sticky>
      <van-nav-bar
          :title="tittle"
          left-text="返回"
          right-text="评论"
          left-arrow
          @click-left="onClickLeft"
          @click-right="onClickRight"
      />
    </van-sticky>
    <div class="form-container mt-10">
      <van-cell-group inset class="mt-10 upload-container">
        <span class="font-weight-500 mt-12 ml-16"> 关联图片(选填)</span>
        <van-uploader :after-read="afterRead" multiple v-show="photoData.length === 0" ref="inputImage" :disabled="!isCanModify">
          <div class="select-wrapper">
            <img :src="require('../../assets/images/quality/add-photo.png')" alt="">
            <span class="font-14">添加问题图片</span>
          </div>
        </van-uploader>
        <div class="selected-wrapper" v-show="photoData.length > 0">
          <ul >
            <li v-for="(item, index) in photoData" :key="index" class="ml-10 mt-10" @click="showImagePre">
              <div class="item-wrapper" v-if="typeof item === 'string'">
                <van-image
                    radius="4"
                    width="70"
                    height="70"
                    :src="item"
                />
                <van-image
                    @click.stop="previewViewPoint(index)"
                    width="20"
                    height="20"
                    class="viewpoint-icon"
                    :src="require('../../assets/images/issue/viewpoint-icon.png')"
                />
              </div>
              <div class="item-wrapper" v-else>
                <van-image
                    radius="4"
                    width="70"
                    height="70"
                    :src="appendPhotoSrc(item , false)"
                />
                <van-image
                    v-if="isCanModify"
                    @click.stop="deletePhotoItem(index)"
                    width="20"
                    height="20"
                    class="delete-icon"
                    :src="require('../../assets/images/quality/delete-photo-icon.png')"
                />
              </div>
            </li>
          </ul>
          <div class="add-icon" v-if="isCanModify">
            <van-image
                radius="4"
                width="30"
                height="30"
                :src="require('../../assets/images/quality/add-photo.png')"
                @click="clickAddPhoto"
            />
          </div>
        </div>
      </van-cell-group>
      <van-cell-group inset class="mt-10">
        <van-field
            :disabled="!isAdd"
            size="large"
            v-model="title"
            name="pattern"
            type="textarea"
            placeholder="请输入问题标题"
            rows="2"
            autosize
            :rules="[{ validator: validatorMessage }]"
        />
      </van-cell-group>
      <van-cell-group inset class="mt-10 pt-12">
        <span class="font-weight-500 mt-12 pt-12 ml-16">基本信息</span>
        <van-field
            v-model="checkStatus.ItemName"
            is-link
            readonly
            name="datePicker"
            label="状态"
            placeholder="点击选择问题状态"
            @click="isShowStatus = true"
        />
        <div v-if="isCanModify && !isAdd">
          <van-popup
              v-model:show="isShowStatus"
              position="bottom"
              closeable
              :style="{ height: '40%' }"
              @close="isShowType = false"
          >
            <van-nav-bar
                title="选择问题状态"
            />
            <ul class="type-list">
              <li v-for="(item) in statusData" :key="item.ItemDetailId" @click="selectStatus(item)">
                <span class="selected-span">{{item.ItemName}}</span>
              </li>
            </ul>
          </van-popup>
        </div>
        <van-field
            v-model="checkType.ItemName"
            is-link
            readonly
            name="datePicker"
            label="类型"
            placeholder="点击选择问题类型"
            @click="isShowType = true"
        />
        <div v-if="isCanModify">
          <van-popup
              v-model:show="isShowType"
              position="bottom"
              closeable
              :style="{ height: '40%' }"
              @close="isShowType = false"
          >
            <van-nav-bar
                title="选择问题类型"
            />
            <ul class="type-list">
              <li v-for="(item) in typeData" :key="item.ItemDetailId" @click="selectType(item)">
                <span class="selected-span">{{item.ItemName}}</span>
              </li>
            </ul>
          </van-popup>
        </div>
        <van-field
            v-model="checkCutoffDate"
            is-link
            readonly
            name="datePicker"
            label="截止日期"
            placeholder="请选择截止日期"
            @click="isShowCutoffPicker = true"
        />
        <div v-if="isCanModify">
          <van-popup v-model:show="isShowCutoffPicker" position="bottom">
            <van-date-picker @confirm="onConfirmStartTime" @cancel="isShowCutoffPicker = false" v-model="currentDate" title="选择日期"/>
          </van-popup>
        </div>
      </van-cell-group>
      <van-cell-group inset class="mt-10 pt-12">
        <span class="font-weight-500 mt-12 pt-12 ml-16">人员</span>
        <div class="zgr-wrapper">
          <div class="zgr-label">
            <span class="label">参与人</span>
            <span class="input ml-50" @click="showUserPop('checkZgr',false)">{{zgrTip}}</span>
            <img :src="require('../../assets/images/common/right-arrow.png')" style="width: 18px;height: 18px" alt="">
          </div>
          <ul class="zgr-list">
            <li v-for="(item) in selectedCyr" :key="item.UserId">
              <span class="selected-span">{{item.RealName}}</span>
            </li>
          </ul>
        </div>
        <!-- 人员列表 -->
        <van-popup
            v-model:show="isShowUser"
            closeable
            position="bottom"
            :style="{ height: '90%' }"
            @close="isShowUser = false"
        >
          <van-nav-bar
              title="请选择人员"
          />
          <van-checkbox-group @change="changeCheck" v-model="cyrChecked">
            <van-cell-group inset class="mt-10">
              <van-cell
                  v-for="(item, index) in userData"
                  clickable
                  :key="item.UserId"
                  :title="item.RealName"
                  @click="toggle(index)"
              >
                <template #right-icon>
                  <van-checkbox
                      :name="item.UserId"
                      :ref="el => checkboxRefs[index] = el"
                      @click.stop
                      :disabled="item.UserId === createUserId"
                  />
                </template>
              </van-cell>
            </van-cell-group>
          </van-checkbox-group>
        </van-popup>
      </van-cell-group>
      <van-cell-group inset class="mt-10 pt-12" v-if="relationFiles.length > 0">
        <span class="font-weight-500 mt-12 pt-12 ml-16">附件</span>
        <van-cell is-link v-for="(item) in relationFiles" :key="item.FileId" :title="item.FileName" @click="previewFile(item)">
          <template #icon>
            <van-image
                class="document-icon"
                width="24px"
                height="24px"
                :src="handleIconSrc(item)"
            />
          </template>
        </van-cell>
      </van-cell-group>
      <van-button v-if="isAdd" class="add-btn" type="primary" @click="submit">提交</van-button>
    </div>
    <Preview :url="previewUrl" @close-dialog="closeDialog" :visible="isPreview"></Preview>
  </div>
</template>

<script setup>
import router from "@/router";
import {onBeforeUpdate, onMounted, ref, watch} from "vue";
import {getCurrentDate} from "@/utils";
import api from "@/api";
import {getToken} from "@/utils/token";
import {getOrganizeId} from "@/utils/organizeId";
import {getOrganizationId} from "@/utils/OrganizationId";
import {showImagePreview, showToast} from "vant";
import {useRoute} from "vue-router";
import {getUserInfo} from "@/utils/user";
import Preview from "@/views/Project/Preview.vue";

const token = ref('')
const organizeId = ref('')
const organizationId = ref('')
const issueId = ref('')
const createUserId = ref('')
const tittle = ref('新建问题')
const isAdd = ref(true)
const isCanModify = ref(false)
const photoData = ref([])
const addphotoData = ref([])
const typeData = ref([])
const statusData = ref([])
const currentDate = ref([])
const title = ref('')
const isShowType = ref(false)
const checkType = ref('')
const isShowStatus = ref(false)
const checkStatus = ref('')
const isShowCutoffPicker = ref(false)
const checkCutoffDate = ref('')
const zgrTip = ref('请选择(选填)')
const selectedCyr = ref([])
const isShowUser = ref(false)
const cyrChecked = ref([]);
const userData = ref([])
const checkboxRefs = ref([]);
const relationFiles = ref([])
const previewUrl = ref('')
const isPreview = ref(false)
const commonTypes = ref(['.ppt','.pptx','.doc','.docx','.xls','.xlsx','.png','.jpeg','.jpg','.gif','.webp','.txt','.pdf','.mp4','.avi','.mkv','.mov','.mp3','.acc','.flac'])

// 校验函数可以直接返回一段错误提示
const validatorMessage = (val) => `${val} 不合法，请重新输入`;
/**
 * 左侧点击回退上一级路由
 * 注：replace 方法不会增加路由信息
 */
function onClickLeft(){
  router.back()
}
/**
 * 进入评论页面
 */
function onClickRight() {
  router.push({
    path:'/issueComment',
    query: {
      issueId: issueId.value
    }
  })
}
/**
 * 文件选择后
 * @param file
 */
const afterRead = (file) => {
  if (isAdd.value) {
    if (file instanceof Array){
      for (const temp of file) {
        photoData.value.push(temp)
      }
    }else {
      photoData.value.push(file)
    }
  }else {
    if (file instanceof Array){
      addphotoData.value = file
    }else {
      addphotoData.value = [file]
    }
    uploadImages()
  }
}
/**
 * 预览图片
 */
function showImagePre() {
  if (!isAdd.value){
    const temp = []
    // 数组重组
    for (const item of photoData.value) {
      if(typeof item === 'string') {
        temp.push(item)
      }else {
        const url = window.IP_CONFIG.BASE_URL + "/" + item.bf_path
        temp.push(url)
      }
    }
    showImagePreview(temp);
  }
}
/**
 * 拼接图片地址
 * @param item
 * @param isRecord
 */
function appendPhotoSrc(item,isRecord) {
  if (isAdd.value){
    return item.content
  }else {
    // 详情图片地址拼接
    return window.IP_CONFIG.BASE_URL + "/" + item.bf_path
  }
}
/**
 * 删除图片
 * @param index
 */
async function deletePhotoItem(index) {
  if (isAdd.value) {
    photoData.value.splice(index, 1)
  } else {
    const pic = photoData.value[index]
    const res = await api.RemoveIssueDocRel({
      Token: token.value,
      IssueId: issueId.value,
      FileId: pic.bf_guid
    })
    if (res.data.Ret === 1) {
      photoData.value.splice(index, 1)
    }
  }
}
/**
 * 点击关联视图图片
 */
function previewViewPoint(index) {
  showToast('请在Web端查看!')
}
/**
 * 手动触发input选取文件点击事件
 * @param type
 */
function clickAddPhoto () {
  const input = document.getElementsByClassName('van-uploader__input')
  input[0].dispatchEvent(new MouseEvent('click'))
}
/**
 * 获取问题状态和类型
 * @returns {Promise<void>}
 */
async function getTypesAndStatus(){
  if (!isAdd.value) {
    const res = await api.GetIssueStatus({
      token: token.value,
      organizeId: organizationId.value
    })
    if (res.data.Ret === 1) {
      statusData.value = res.data.Data
    }
  }
  const res1 = await api.GetIssueTypes({
    token: token.value,
    organizeId: organizationId.value
  })
  if (res1.data.Ret === 1) {
   typeData.value = res1.data.Data
  }
}
/**
 * 选择问题类型
 * @param item
 */
async function selectType(item) {
  if (isAdd.value) {
    checkType.value = item
    isShowType.value = false
  }else {
    isShowType.value = false
    const res = await api.ModifyIssueType({
      Token: token.value,
      IssueId: issueId.value,
      Id: item.ItemDetailId
    })
    if(res.data.Ret === 1) {
      checkType.value = item
    }else {
      showToast(res.data.Msg)
    }
  }
}
/**
 * 选择问题状态
 * @param item
 */
async function selectStatus(item) {
  if (!isAdd.value) {
    isShowStatus.value = false
    const res = await api.ModifyIssueStatus({
      Token: token.value,
      IssueId: issueId.value,
      Id: item.ItemDetailId
    })
    if(res.data.Ret === 1) {
      checkStatus.value = item
    }else {
      showToast(res.data.Msg)
    }
  }
}
/**
 * 截止日期确认
 * @param selectedValues
 */
async function onConfirmStartTime(selectedValues) {
  if (isAdd.value) {
    checkCutoffDate.value = selectedValues.selectedValues.join('-')
    isShowCutoffPicker.value = false;
  }else {
    const date = selectedValues.selectedValues.join('-')
    isShowCutoffPicker.value = false;
    const res = await api.ModifyIssueEndDate({
      Token: token.value,
      IssueId: issueId.value,
      EndDate: date
    })
    if(res.data.Ret === 1) {
      checkCutoffDate.value  = date
    }else {
      showToast(res.data.Msg)
    }
  }
}
/**
 * 详情参与人移除
 */
async function removeIssueJoiner(userId) {
  const res = await api.RemoveIssueJoiner({
    Token: token.value,
    IssueId: issueId.value,
    Id: userId
  })
  if(res.data.Ret === 1) {
    showToast('移除成功')
    let index = selectedCyr.value.findIndex((item) => item.UserId === userId)
    if (index !== -1) {
      selectedCyr.value.splice(index, 1)
    }
  }else {
    showToast(res.data.Msg)
  }
}
/**
 * 详情参与人添加
 */
async function addIssueJoiner(userId) {
  const res = await api.AddIssueJoiner({
    Token: token.value,
    IssueId: issueId.value,
    Id: userId
  })
  if(res.data.Ret === 1) {
    showToast('添加成功')
    let index = userData.value.findIndex((item) => item.UserId === userId)
    if (index !== -1) {
      selectedCyr.value.push(userData.value[index])
    }
  }else {
    showToast(res.data.Msg)
  }
}
/**
 * 显示选择人员弹窗
 */
function showUserPop(type,isCheck) {
  isShowUser.value = true
}
/**
 * 复选框变化监听方法
 * @param val
 */
function changeCheck(val) {
  if (isAdd.value){
    selectedCyr.value = []
    for (const valElement of val) {
      let index = userData.value.findIndex((item) => item.UserId === valElement)
      selectedCyr.value.push(userData.value[index])
    }
    console.log('参与人',selectedCyr.value)
  }else {
    if (val.length < selectedCyr.value.length) {
      // 移除
      for (const item of selectedCyr.value) {
        let index = val.findIndex((valElement) => valElement === item.UserId)
        if (index === -1) {
          removeIssueJoiner(item.UserId)
          break
        }
      }
    }else {
      // 添加
      for (const valElement of val) {
        let index = selectedCyr.value.findIndex((item) => item.UserId === valElement)
        if (index === -1) {
          addIssueJoiner(valElement)
          break
        }
      }
    }
  }
}
/**
 * 人员选择
 * @param index
 */
const toggle = (index) => {
  if(!isAdd.value) {
    const item = userData.value[index]
    if (item.UserId === createUserId.value) {
      return
    }
  }
  checkboxRefs.value[index].toggle()
}
/**
 * 获取所有人员
 */
async function getUser() {
  const res = await api.GetUserPaged(
      {
        PageNum: 1,
        PageSize: 9999,
        KeyWord: '',
        OrganizeId: organizeId.value,
        searchType: 0,
        RoleId: '',
        Token: getToken()
      }
  )
  if (res.data.Ret === 1) {
    userData.value = res.data.Data.list
  }
}
/**
 * 上传文件
 * @returns {Promise<void>}
 */
async function uploadImages() {
  const fd = new FormData
  if (isAdd.value) {
    for (const file of photoData.value) {
      fd.append('Files',file.file)
    }
  }else {
    for (const file of addphotoData.value) {
      fd.append('Files',file.file)
    }
  }
  fd.append('Token',getToken())
  fd.append('IssueId', issueId.value ? issueId.value : '')
  const res = await api.UploadImages(fd)
  if (res.data.Ret === 1 ){
    const temp = res.data.Data
    if (isAdd.value) {
      let uploadFileId =''
      for (const tempElement of temp) {
        uploadFileId += tempElement.bf_guid+','
      }
      await AddIssueWithFileId(uploadFileId)
    }else {
      photoData.value.push(...temp)
    }
  }else {
    showToast(res.data.Msg)
  }
}
/**
 * 保存问题内容
 */
async function AddIssueWithFileId(fileId) {
  let cyr = ''
  selectedCyr.value.forEach(ele => {
    cyr += ele.UserId + ','
  })
  const param = {
    Token: token.value,
    Title: title.value,
    // IssueStatusID: checkStatus.value.ItemDetailId,
    IssueTypeId: checkType.value.ItemDetailId,
    EndDateStr: checkCutoffDate.value,
    JoinerIds: cyr,
    OrganizeId: organizeId.value,
    ImageIds: fileId,
    ModelID: '',
    ImageUrl: '',
    ViewPointID: '',
    FileIds: '',
    RealName: ''
  }
  const res = await api.AddIssue(param)
  if (res.data.Ret === 1) {
    router.back()
    showToast(res.data.Msg)
  }
}
/**
 * 判断是否有图片上传
 */
function submit() {
  // 新建
  if (isAdd.value){
    if (!title.value){
      showToast('必须填写问题标题')
      return
    } else if (!checkType.value) {
      showToast('必须选择问题类型')
      return
    } else if (!checkCutoffDate.value) {
      showToast('必须选择截止日期')
      return
    }
    if (photoData.value.length > 0){
      uploadImages()
    }else {
      AddIssueWithFileId('')
    }
  }
}
/**
 * 获取问题详情
 */
async function getIssueDetail() {
  const res = await api.GetIssueDetail({
    token: token.value,
    issueId: issueId.value
  })
  if (res.data.Ret === 1) {
    const issueDetail = res.data.Data
    const issueObj = issueDetail.issueObj
    createUserId.value = issueObj.CreateUserId
    title.value = issueObj.Title 
    checkStatus.value = {
      ItemName: issueObj.IssueStatusText,
      ItemDetailId: issueObj.IssueStatus
    },
    checkType.value = {
      ItemName: issueObj.IssueTypeText,
      ItemDetailId: issueObj.IssueTypeID
    }
    checkCutoffDate.value = issueObj.EndDate.substring(0, 10)
    // 参与人
    let cyrId = []
    const joiners = issueDetail.joiners.map(item => {
      item.UserId = item.Id
      cyrId.push(item.Id)
      return item
    })
    cyrChecked.value = cyrId
    selectedCyr.value = joiners
    if (issueObj.ImageUrl) {
      photoData.value.push(issueObj.ImageUrl)
    }
    photoData.value.push(...issueDetail.base_files)
    relationFiles.value = issueDetail.relationFiles
    if (issueDetail.isIssueManager && issueObj.DeleteMark !== 2) {
      isCanModify.value = true
    }else {
      isCanModify.value = false
    }
  }
}
/**
 * 附件中文件图片类型
 */
function handleIconSrc(file) {
  const extension = file.FileName.split('.').pop()
  if (extension){
    switch (extension.toLowerCase()) {
      case 'ppt':
      case 'pptx':
        return require('../../assets/images/document/ppt-icon.png')
      case 'doc':
      case 'docx':
        return require('../../assets/images/document/word-icon.png')
      case 'dwg':
        return require('../../assets/images/document/dwg-icon.png')
      case 'xls':
      case 'xlsx':
        return require('../../assets/images/document/excel-icon.png')
      case 'png':
      case 'jpeg':
      case 'jpg':
      case 'gif':
      case 'webp':
        return require('../../assets/images/document/image-icon.png')
      case 'txt':
        return require('../../assets/images/document/txt-icon.png')
      case 'pdf':
        return require('../../assets/images/document/pdf-icon.png')
      case 'mp4':
      case 'avi':
      case 'mkv':
      case 'mov':
        return require('../../assets/images/document/mp4-icon.png')
      case 'mp3':
      case 'acc':
      case 'flac':
        return require('../../assets/images/document/mp3-icon.png')
      case 'zip':
      case 'rar':
        return require('../../assets/images/document/zip-icon.png')
      case 'rvt':
        return require('../../assets/images/document/rvt-icon.png')
      default:
        return require('../../assets/images/document/unknown-icon.png')
    }
  }else {
    return require('../../assets/images/document/folder-icon.png')
  }
}
/**
 * 附件预览
 */
function previewFile(item) {
  const FileExtension = '.' + item.FileName.split('.').pop().toLowerCase()
  if (commonTypes.value.includes(FileExtension)){
    const tempUrl = `${window.IP_CONFIG.BASE_URL}/api/v1/attach/preview?id=${item.FileId}&Token=${getToken()}`
    previewUrl.value = `${window.IP_CONFIG.BASE_URL}/Content/PDFJS/web/viewer.html?file=${encodeURIComponent(tempUrl)}`;
    isPreview.value = true
  }else {
    showToast('暂不支持的格式，无法打开！')
  }
}
function closeDialog(){
  isPreview.value = false
}
onMounted(()=>{
  const route = useRoute()
  issueId.value = route.query.issueId
  if (issueId.value) {
    isAdd.value = false
    tittle.value = '详情'
  } else {
    isAdd.value = true
    tittle.value = '新建问题'
    isCanModify.value = true
    checkStatus.value = {
      ItemName: '打开',
      ItemDetailId: ''
    }
  }
  organizeId.value = getOrganizeId()
  organizationId.value = getOrganizationId()
  token.value = getToken()
  currentDate.value = getCurrentDate()
  if (!isAdd.value) {
    getIssueDetail()
  }
  getTypesAndStatus()
  getUser()
})
</script>

<style scoped lang="scss">
.add-container{
  height: 100%;
  background-color: #F4F5F6;
  .form-container{
    .upload-container{
      display: flex;
      flex-direction: column;
      padding-bottom: 10px;
      ul{
        display: flex;
        overflow: auto;
      }
      :deep(.van-uploader__input-wrapper){
        width: 100%;
      }
      .select-wrapper{
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100px;
        img{
          width: 24px;
          height: 24px;
        }
      }
      .selected-wrapper{
        display: flex;
        position: relative;
        ul{
          margin-right: 85px;
        }
        .item-wrapper{
          position: relative;
          display: flex;
          flex-direction: column;
          border-radius: 4px;
          background-color: white;
          margin: 16px;
          .delete-icon{
            position: absolute;
            right: 0;
            top: 0;
          }
          .viewpoint-icon{
            position: absolute;
            right: 5px;
            bottom: 5px;
          }
        }
        .add-icon{
          border-radius: 4px;
          display: flex;
          justify-content: center;
          align-items: center;
          margin-top: 26px;
          margin-right: 10px;
          width: 70px;
          height: 70px;
          background-color: #E9EBED;
          right: 0;
          position: absolute;
        }
      }
    }
    .type-list{
      display: flex;
      flex-wrap: wrap;
      margin-top: 30px;
      .selected-span{
        display: block;
        margin: 16px 0 0 16px;
        font-size: 14px;
        color: #616F7D;
        background-color: #F4F5F6;
        padding: 10px;
      }
    }
    .zgr-wrapper{
      padding: 16px;
      .zgr-label{
        display: flex;
        position: relative;
        span{
          font-size: 14px;
        }
        .input{
          color: #c8c9cc;
          padding-right: calc(100% - 200px - 40px);
        }
        img{
          position: absolute;
          right: 0;
        }

      }
      .zgr-list{
        margin-left: 90px;
        display: flex;
        flex-wrap: wrap;
        margin-top: 10px;
        .selected-span{
          display: block;
          margin: 8px 0 0 8px;
          font-size: 12px;
          color: white;
          background-color: #007AFF;
          padding: 4px;
        }
      }
    }
    .document-icon{
      margin-top: 2px;
      margin-right: 6px;
    }
    .add-btn{
      width: calc(100% - 32px);
      margin: 32px 16px 32px 16px
    }
  }
}

</style>

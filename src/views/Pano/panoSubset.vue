<template>
  <div class="list-container">
    <van-sticky>
      <van-nav-bar
        :title="title"
        left-text="返回"
        left-arrow
        @click-left="onClickLeft"
      />
      <van-dropdown-menu v-if="isBusiness">
        <van-dropdown-item v-model="organizeId" :options="projectOption"/>
      </van-dropdown-menu>
    </van-sticky>
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
      <van-list
        class="list"
        v-model:loading="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="onLoad">
        <div v-for="item in listData" :key="item.ExamineID" @click="gotoDetails(item)">
          <van-swipe-cell>
            <div class="item-wrapper">
              <div class="left-wrapper">
                <van-image
                  class="left-img"
                  :src="getImageUrl(item)"
                />
              </div>
              <div class="right-wrapper">
                <div class="name">{{item.PchChname}}</div>
                <div class="time">{{'所属图集: ' + title}}</div>
                <div class="time">{{'采集时间: ' + item.PsCollecteddatetime.split(' ')[0]}}</div>
              </div>
            </div>
            <template #right>
              <div class="item-right-wrapper">
                <img :src="require('../../assets/images/quality/delete-item.png')" alt="" @click="deleteItem(item)">
              </div>
            </template>
          </van-swipe-cell>
        </div>
      </van-list>
    </van-pull-refresh>
    <van-image
      class="add-img"
      width="60"
      height="60"
      :src="require('../../assets/images/common/add-btn.png')"
      @click="addPanoSubset"
    ></van-image>
  </div>
</template>

<script setup>
import router from "@/router";
import {onBeforeUpdate, onMounted, ref, watch} from "vue";
import api from "@/api";
import {getToken} from "@/utils/token";
import {getOrganizeId} from "@/utils/organizeId";
import {showToast} from "vant";
import {useRoute} from "vue-router";
import {getUserInfo} from "@/utils/user";
const route = useRoute();
const token = ref('');
const organizeId = ref('')
const title = ref('');
const PbGuid = ref('');
const PbUrl = ref('');
const loading = ref(false);
const finished = ref(false);
const refreshing = ref(false);
const listData = ref([]);
/**
 * 刷新列表
 */
function onRefresh() {
  finished.value = false;
  loading.value = true;
  getList();
  refreshing.value = false
}
/**
 * 左侧点击回退上一级路由
 * 注：replace 方法不会增加路由信息
 */
function onClickLeft(){
  router.back()
}
/***
 * 获取图片地址
 */
function getImageUrl(item) {
  return `${window.IP_CONFIG.BASE_URL}/Panorama${PbUrl.value}/vtour/panos/${item.PsScenename}.tiles/thumb.jpg`
}
/**
 * 列表加载方法
 */
function onLoad(){
}
/**
 * 获取子集数据
 */
async function getList() {
  loading.value = true
  const res = await api.GetPanoramaSceneList({
    Token: token.value,
    pbGuid: PbGuid.value
  })
  if (res.data.Ret === 1){
    listData.value = res.data.Data
  }
  finished.value = true
  loading.value = false
}
/**
 * 删除单条数据
 * @param id
 * @returns {Promise<void>}
 */
async function deleteItem(item) {
  const res = await api.RemoveScene({
    Token: token.value,
    PbGuid: item.PbGuid,
    SceneName: item.PsScenename,
    ProjectId: organizeId.value
  })
  if (res.data.Ret === 1){
    showToast(res.data.Msg)
    await getList()
  }else {
    showToast(res.data.Msg)
  }
}
/**
 * 添加子集
 */
function addPanoSubset() {
  router.push({
    path:'/addPano',
    query: {
      PbGuid: PbGuid.value
    }
  })
}
/**
 * 路由跳转预览页面
 */
function gotoDetails(item) {
  router.push({
    path:'/panoPreview',
    query: {
      PbGuid: item.PbGuid,
      ProjectId: organizeId.value,
      SceneName: item.PsScenename
    }
  })
}
onMounted(()=>{
  token.value = getToken()
  organizeId.value = getOrganizeId()
  PbGuid.value = route.query.PbGuid
  PbUrl.value = route.query.PbUrl
  title.value = route.query.Name
  if (PbGuid.value) {
    getList()
  }
})

</script>
<style scoped lang="scss">
.list-container{
  height: 100%;
  background-color: #f2f2f2;
  ::-webkit-scrollbar{
    display: none;
  }
  .list{
    padding: 16px;
    overflow-y: scroll;
    .item-wrapper{
      display: flex;
      margin-bottom: 16px;
      padding: 12px;
      border-radius: 4px;
      background-color: white;
      .left-wrapper{
        width: 118px;
        height: 88px;
        border-radius: 4px;
        .left-img{
          width: 100%;
          height: 100%;
        }
      }
      .right-wrapper{
        margin-left: 8px;
        width: calc(100% - 126px);
        .name{
          width: 100%;
          height: 22px;
          font-size: 16px;
          font-weight: 500;
          color: #323233;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .time{
          width: 100%;
          height: 22px;
          margin-top: 10px;
          font-size: 16px;
          font-weight: 400;
          color: #7a7a7d;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
    .item-right-wrapper{
      height: 100%;
      img{
        width: auto;
        height: auto;
        max-width: 100%;
        max-height: 100%;

      }
    }
  }
  .add-img{
    position: fixed;
    bottom: 100px;
    right: 16px;
  }
}

</style>

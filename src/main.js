import { createApp } from 'vue'
import { Tab, Tabs, Progress, showLoadingToast, closeToast, showToast, Search, List, PullRefresh, Cell,Tabbar, TabbarItem, NavBar, Form, Field, CellGroup, Uploader, Picker, DatePicker, Popup, ContactList, Checkbox, CheckboxGroup, Tag, Divider ,Sticky ,DropdownMenu, DropdownItem, SwipeCell ,Button, ImagePreview, Popover, Icon,RadioGroup,Radio} from 'vant';
import { Image as VanImage } from 'vant';
import 'vant/lib/index.css'
import App from './App.vue'
import router from './router'
import store from './store'

import api from './api'
import 'animate.css';
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import 'element-plus/theme-chalk/dark/css-vars.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
require('./style/reset.scss')
require('./style/base.scss')
require('./assets/font/iconfont.css')
// require('./assets/icomoon-svg/style.css')



const app = createApp(App);

for (const [key, comp] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, comp)
}
app.config.globalProperties.$api = api
app.config.globalProperties.$ip = baseURL => {
    if (process.env.NODE_ENV === 'production') {
      return window.IP_CONFIG[baseURL]
    } else {
      return require('./config/proxyIP')[baseURL]
    }
  }
app.use(ImagePreview)
app.use(Button)
app.use(SwipeCell)
app.use(DropdownMenu)
app.use(DropdownItem)
app.use(Sticky)
app.use(Divider)
app.use(Tag)
app.use(Checkbox)
app.use(CheckboxGroup)
app.use(ContactList)
app.use(Popup)
app.use(Picker)
app.use(DatePicker)
app.use(Uploader)
app.use(Form)
app.use(Field)
app.use(CellGroup)
app.use(NavBar)
app.use(Tabbar)
app.use(TabbarItem)
app.use(VanImage)
app.use(Cell)
app.use(PullRefresh)
app.use(List)
app.use(Search)
app.use(store)
app.use(router)
app.use(ElementPlus)
app.mount('#app')
app.use(Tab)
app.use(Tabs)
app.use(Progress)
app.use(showLoadingToast)
app.use(showToast)
app.use(closeToast)
app.use(Popover)
app.use(Icon)
app.use(Radio)
app.use(RadioGroup)



import { createStore } from 'vuex'
import menuList from './menuList'
import dialog from './dialog'
import code from "@/store/code";
import createPersistedState from 'vuex-persistedstate'


export default createStore({
  state: {
  },
  mutations: {
  },
  actions: {
  },
  modules: {
      menuList,
      dialog,
      code,
  },
    plugins: [createPersistedState({
        storage: window.sessionStorage,
        reducer(val) {
        return {
            menuList: val.menuList,
            dialog: val.dialog,
            code: val.code
        }
        }
    })]
})

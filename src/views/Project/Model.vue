<template>
  <div class="list-container">
    <van-sticky>
      <van-nav-bar
          :title="getProjectName()"
          left-text="返回"
          left-arrow
          @click-left="onClickLeft"
      />
    </van-sticky>
    <div class="model">
      <div class="phase">
        <van-list
          v-model:loading="loading"
          :finished="finished"
          finished-text=""
          @load="onLoad"
        >
          <van-cell v-for="item in phases" :key="item.Id" :title="item.MenuName" :class="item.active ? 'active' : ''" @click="changePhase(item)"/>
        </van-list>
      </div>
      <div class="list">
        <van-list
          v-model:loading="loading1"
          :finished="finished1"
          finished-text="没有更多了">
          <div v-for="item in listData" :key="item.featureID" @click="gotoPreview(item)">
            <van-swipe-cell>
              <div class="item-wrapper">
                <div class="left-wrapper">
                  <van-image
                      class="left-img"
                      :src="'data:image/png;base64,' + item.thumbnail"
                  />
                </div>
                <div class="right-wrapper">
                  <div class="name">{{item.featureName}}</div>
                  <div class="time">{{item.createTime.split('T')[0]}}</div>
                </div>
              </div>
              <template #right>
                <div class="item-right-wrapper">
                  <img :src="require('../../assets/images/model/model-preview.png')" alt="" @click="gotoPreview(item)">
                  <!-- <van-button square text="加载" type="primary" class="load-button" /> -->
                </div>
              </template>
            </van-swipe-cell>
          </div>
        </van-list>
      </div>
    </div>
    <Preview :url="previewUrl" @close-dialog="closeDialog" v-if="isPreview"></Preview>
  </div>
</template>
<script setup>
import router from "@/router";
import {onMounted, ref} from "vue";
import api from "@/api";
import {getOrganizeId} from "@/utils/organizeId";
import {showToast} from "vant";
import {getToken} from "@/utils/token";
import {getProjectName} from "@/utils/projectName";
import Preview from "@/views/Model/Preview.vue";
const loading = ref(false);
const finished = ref(false);
const loading1 = ref(false);
const finished1 = ref(false);
const listData = ref([])
const phases = ref([])
const token = ref('')
const organizeId = ref('')
const previewUrl = ref('')
const isPreview = ref(false)
/**
 * 左侧点击回退上一级路由
 * 注：replace 方法不会增加路由信息
 */
function onClickLeft(){
  router.push('/home')
}
/**
 * 列表加载方法
 */
function onLoad(){
}
/**
 * 获取阶段所有子阶段
 */
function getChildrenPhase(dict, arr) {
  arr.push(dict.BusinessCode)
  let children = dict.Children
  for (const item of children) {
    getChildrenPhase(item, arr)
  }
  return arr
}
/**
 * 加载模型阶段
 */
async function getUserMenuTree() {
  loading.value = true
  const res = await api.GetUserMenuTree({
    Token: token.value,
    organizeId: organizeId.value,
    parentId: '0'
  })
  if (res.data.Ret === 1) {
    const data = res.data.Data
    for (const menu of data) {
      if (menu.MenuCode === 'MODEL') {
        let arrM = []
        const phaseArr = menu.Children
        for (let phase of phaseArr) {
          if(phase.BusinessCode !== 'allmodel_phase'){
            let array = []
            const arr = getChildrenPhase(phase, array)
            phase.Children = arr
            arrM.push(phase)
          }
        }
        phases.value = arrM;
        loadAllMode()
        break
      }
    }
  } else {
    showToast(res.data.Msg)
  }
  finished.value = true
  loading.value = false
}
/**
 * 加载全部模型
 */
async function loadAllMode() {
  loading1.value = true
  const res = await api.GetAllFeatures({
    VaultID: organizeId.value
  })
  if (res.status === 200) {
    let modelsArr = res.data
    for (const phase of phases.value) {
      const child = phase.Children
      let arrM = []
      for (const model of modelsArr) {
        const index = child.findIndex(ele => ele === model.phase)
        if (index !== -1) {
          arrM.push(model)
        }
      }
      phase.modelsArr = arrM
    }
    phases.value[0].active = true
    listData.value = phases.value[0].modelsArr
  } else {
    showToast('加载模型失败!')
  }
  finished1.value = true
  loading1.value = false
}
/**
 * 点击模型阶段
 */
function changePhase(phase) {
  phases.value.forEach(element => {
    element.active = false
  })
  phase.active = true
  listData.value = phase.modelsArr
}
/**
 * 跳转路由到预览页面
 */
function gotoPreview(item) {
  previewUrl.value = `${window.IP_CONFIG.FRONT_URL}/scenemanager/#/?vaultID=${organizeId.value}&modelID=${item.featureID}`;
  isPreview.value = true
}
function closeDialog(){
  isPreview.value = false
}
onMounted(()=>{
  token.value = getToken()
  organizeId.value = getOrganizeId()
  getUserMenuTree()
})
</script>

<style scoped lang="scss">
.list-container{
  height: calc(100% - 96px);
  background-color: #f2f2f2;
  .model{
    height: 100%;
    display: flex;
    ::-webkit-scrollbar{
      display: none;
    }
    .phase{
      width: 110px;
      height: 100%;
      background-color: #FFF;
      overflow-y: scroll;
      .active{
        background-color: rgb(242, 242, 242);
      }
    }
    .list{
      width: calc(100% - 110px);
      height: 100%;
      padding: 16px;
      overflow-y: scroll;
    }
  }
  .item-wrapper{
    display: flex;
    margin-bottom: 16px;
    padding: 12px;
    border-radius: 4px;
    background-color: white;
    .left-wrapper{
      width: 88px;
      height: 88px;
      border-radius: 4px;
      .left-img{
        width: 100%;
        height: 100%;
      }
    }
    .right-wrapper{
      margin-left: 8px;
      width: calc(100% - 96px);
      .name{
        width: 100%;
        height: 48px;
        font-size: 16px;
        font-weight: 500;
        color: #323233;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .time{
        width: 100%;
        margin-top: 16px;
        font-size: 16px;
        font-weight: 400;
        color: #7a7a7d;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
  .item-right-wrapper{
    height: 100%;
    .load-button{
      height: 100%;
    }
    img{
      width: auto;
      height: auto;
      max-width: 100%;
      max-height: 100%;

    }
  }
}
</style>
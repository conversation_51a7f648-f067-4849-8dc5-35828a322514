/**
 * 流程接口
 */

 import { request } from '@/utils/request'
 export default {
  GetFlowList: (urlParams,params) => request({ url: `/WF/Comm/Handler.ashx?DoType=HttpHandler&DoMethod=${urlParams}&HttpHandlerName=BP.WF.HttpHandler.WF`, method: 'post', params, baseURL: 'FLOW_URL', headers: { 'Content-Type': 'multipart/form-data' }}),
  CCFlowLogin: params => request({url: '/api/User/User/CCFlowLogin', method: 'post', params}),

 }
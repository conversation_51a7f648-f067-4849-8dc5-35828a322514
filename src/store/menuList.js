export default {
    state: {
        //存放当前已经激活的菜单按钮，通过菜单name判断。适用于多个菜单可同时激活
        menuActive: [],
        //激活的底部菜单按钮，同一时间只能激活一个，所以用字符串保存
        bottomMenuActive: "",
    },

    mutations: {
        toggleMenuActive(state,name) {
            let index = state.menuActive.indexOf(name)
            index === -1 ? state.menuActive.push(name) : state.menuActive.splice(index,1)
        },

        toggleBottomMenuActive(state,name) {
            if (state.bottomMenuActive === name) {
                state.bottomMenuActive = ''
            } else {
                state.bottomMenuActive = name
            }
        }
    }
}
import { computed } from 'vue'
import { mapState, useStore } from 'vuex'

export function useState(mapper) {
    // 拿到store
    const store = useStore()

    // 获取到对应的对象的functions: {name: function, age: function}
    const storeStateFns = mapState(mapper)

    // 对数据进行转换
    const storeState = {}
    Object.keys(storeStateFns).forEach(fnKey => {
        const fn = storeStateFns[fnKey].bind({$store: store})
        storeState[fnKey] = computed(fn)
    })

    return storeState
}



/*
使用：
<div>{{count}}</div>
<div>{{name}}</div>
<div>{{age}}</div>

import {useState} from '@/utils/useState'
export default {
  setup(){
    const storeState = useState(['count','name','age'])
    console.log(storeState.count,storeState.name,storeState.name)
    return {
      ...storeState
    }
  }
}
*/
<template>
  <div class="add-comments">
    <van-sticky>
      <van-nav-bar
          title="新增评论"
          left-arrow
          @click-left="onClickLeft"
      >
      </van-nav-bar>
    </van-sticky>
    <div class="container">
      <div class="comments">
        <van-field
            class="commments-field"
            size="large"
            v-model="title"
            name="pattern"
            type="textarea"
            placeholder="请输入评论内容"
            autosize
        />
      </div>
      <van-button class="add-btn" type="primary" @click="submit">评论</van-button>
    </div>
  </div>
</template>
<script setup>
import router from "@/router";
import {onBeforeUpdate, onMounted, ref, watch} from "vue";
import {getCurrentDate} from "@/utils";
import api from "@/api";
import {getToken} from "@/utils/token";
import {getOrganizeId} from "@/utils/organizeId";
import {getOrganizationId} from "@/utils/OrganizationId";
import {showImagePreview, showToast} from "vant";
import {useRoute} from "vue-router";
import {getUserInfo} from "@/utils/user";
import Preview from "@/views/Project/Preview.vue";
const issueId = ref('')
const organizeId = ref('')
const token = ref('')
const title = ref('')

/**
 * 左侧点击回退上一级路由
 * 注：replace 方法不会增加路由信息
 */
function onClickLeft(){
  router.back()
}
/**
 * 提交评论
 */
async function submit() {
  const res = await api.AddComment({
    Token: token.value,
    organizeId: organizeId.value,
    RelationIssueId: issueId.value,
    Title: title.value,
    AtUserIds: ''
  })
  if (res.data.Ret === 1) {
    router.back()
    showToast(res.data.Msg)
  }else {
    showToast(res.data.Msg)
  }
}
onMounted(()=>{
  const route = useRoute()
  issueId.value = route.query.issueId
  organizeId.value = getOrganizeId()
  token.value = getToken()
})
</script>
<style scoped lang="scss">
.add-comments{
  height: 100%;
  background-color: #F4F5F6;
  .container{
    height: calc(100% - 53px);
    .comments{
      height: calc(100% - 100px - 16px);
      margin: 16px 16px 0px 16px;
      .commments-field{
        height: 100%;
        border-radius: 6px;
      }
    }
    .add-btn{
      width: calc(100% - 32px);
      margin: 32px 16px 0px 16px
    }
  }
}
</style>
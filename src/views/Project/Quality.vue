<template>
  <div class="list-container">
    <van-sticky>
      <van-nav-bar
          v-if="isApp"
          title="质量巡检"
          left-text="返回"
          left-arrow
          @click-left="onClickLeft"
      />
      <van-dropdown-menu v-if="isBusiness">
        <van-dropdown-item v-model="organizeId" :options="projectOption"/>
      </van-dropdown-menu>
      <van-dropdown-menu :key="typeKey">
        <van-dropdown-item v-model="statusValue" :options="statusOption" />
        <van-dropdown-item v-model="typeValue"   :options="typeOption" />
        <van-dropdown-item v-model="classValue" :options="classOption" />
      </van-dropdown-menu>
    </van-sticky>
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
      <van-list
          v-model:loading="loading"
          :finished="finished"
          finished-text="没有更多了"
          @load="onLoad">

          <div class="item-wrapper" v-for="item in listData" :key="item.ExamineID" @click="gotoDetails(item)">
            <van-swipe-cell>
              <div class="top-wrapper">
                <div class="name-bg">
                  <span>{{item.bu_checker_RealName ? item.bu_checker_RealName.split('')[item.bu_checker_RealName.length - 1] : '无'}}</span>
                </div>
                <div class="right-wrapper">
                  <span>{{'检查人：'+item.bu_checker_RealName}}</span>
                 <div class="wrapper-creat">
                   <span style="color: #616F7D">{{item.bu_examiner_name}}</span>
                   <span style="color: #A6AEB6">{{appendStr(item.CreateDate)}}</span>
                 </div>
                </div>
                <div class="status-wrapper">
                  <span :class="getBgStyle(item.ExamineResult)">{{getStyle(item.ExamineResult)}}</span>

                </div>
              </div>
              <div class="center-wrapper">
                <span>{{item.aedt_name}}</span>
                <span>{{item.ExamineRemark}}</span>
              </div>
              <div class="bottom-wrapper">
                <span>任务期限</span>
                <span>{{spitStr(item.ExamineDate,item.RectificateDate)}}</span>

              </div>
              <template #right v-if="item.ExaminerID === userId">
                <div class="item-right-wrapper">
                  <img :src="require('../../assets/images/quality/close-item.png')" alt="" @click="closeItem(item.ExamineID)">
                  <img :src="require('../../assets/images/quality/delete-item.png')" alt="" @click="deleteItem(item.ExamineID)">
                </div>
              </template>

            </van-swipe-cell>
          </div>
      </van-list>
    </van-pull-refresh>
    <van-image
        width="60"
        height="60"
        :src="require('../../assets/images/common/add-btn.png')"
        @click="addQuality"
    ></van-image>
  </div>

</template>
<script setup>
import router from "@/router";
import {onMounted, ref, watch} from "vue";
import api from "@/api";
import {getOrganizeId, setOrganizeId} from "@/utils/organizeId";
import {useRoute} from "vue-router";
import {showToast} from "vant";
import {getToken, setToken} from "@/utils/token";
import {setUserInfo} from "@/utils/user";
import {getUserInfo} from "@/utils/user";
const statusValue = ref('');
const classValue = ref('');
const typeValue = ref('');
const type = ref(1)
const statusOption = [
  { text: '全部状态', value: '' },
  { text: '待检查', value: 'A_ToBeCheck' },
  { text: '待整改', value: 'B_ToBeRectified' },
  { text: '待验收', value: 'C_ToBeRecheck' },
  { text: '已合格', value: 'D_Qualified' },
  { text: '已关闭', value: 'E_Closed' },

];
let typeOption = [
  { text: '全部分类', value: '' }
];
const classOption = [
  { text: '全部等级', value: '' },
  { text: '一般', value: '一般' },
  { text: '严重', value: '严重' },
  { text: '非常严重', value: '非常严重' },

];
let loading = ref(false);
const finished = ref(false);
const refreshing = ref(false);
/**
 * 刷新列表
 */
function onRefresh() {
  // 清空列表数据
  finished.value = false;
  // 重新加载数据
  // 将 loading 设置为 true，表示处于加载状态
  loading.value = true;
  // 加载数据
  getList();
  // 取消刷新状态
  refreshing.value = false
}
/**
 * 左侧点击回退上一级路由
 * 注：replace 方法不会增加路由信息
 */
function onClickLeft(){
  router.back()
  /*  try {
      if (navigator.userAgent.indexOf('Android') > -1){
        mjs.clickBack()
      }else if (navigator.userAgent.indexOf('iPhone') > -1) {
        window.webkit.messageHandlers.clickBack.postMessage(null)
      }
    }catch(error){

    }*/
}
/**
 * 列表加载方法
 */
function onLoad(){
}
const listData = ref([])
/**
 * 获取质量数据
 */
async function getList() {
  loading.value = true
  const res = await api.GetMissions({
    Token: token.value,
    LinkType: type.value,
    OrganizeId: organizeId.value,
    StateType: statusValue.value,
    Types: typeValue.value,
    Severitylevel: classValue.value
  })
  if (res.data.Ret === 1){
    listData.value = res.data.Data.List
  }
  finished.value = true
  loading.value = false
}

/**
 * 拼接数据
 * @param str
 * @returns {string}
 */
function appendStr(str){
  const temp = str.split(' ')[0]
  return ' 于'+ temp + '发起检查'
}

/**
 * 开始时间和结束时间
 * @param startTime
 * @param endTime
 * @returns {string}
 */
function spitStr(startTime, endTime) {
  const tempStart = startTime.split(' ')[0]
  const tempEnd = endTime.split(' ')[0]
  return tempStart+' - '+ tempEnd

}
const statusStyle= ref('')

/**
 * 获取状态
 * @param result
 */
function getStyle(result) {
  let temp = ''
  switch (result) {
    case 'A_ToBeCheck':
      temp ='待检查'
      break
    case 'B_ToBeRectified':
      temp ='待整改'
      break
    case 'C_ToBeRecheck':
      temp ='待验收'
      break
    case 'D_Qualified':
      temp = '已合格'
      break
    case 'E_Closed':
      temp = '已关闭'
      break
  }
  return temp
}
/**
 * 获取状态
 * @param result
 */
function getBgStyle(result) {
  let temp = ''
  switch (result) {
    case 'A_ToBeCheck':
      temp = 'status-toBeCheck'
      break
    case 'B_ToBeRectified':
      temp = 'status-toBeRectified'
      break
    case 'C_ToBeRecheck':
      temp = 'status-toBeRecheck'
      break
    case 'D_Qualified':
      temp = 'status-qualified'
      break
    case 'E_Closed':
      temp = 'status-closed'
      break
  }
  return temp
}
const typeKey = ref(0)
/**
 * 获取检查类型
 * @returns {Promise<void>}
 */
async function getTypes(){
  const res = await api.GetExamTypes(
      {
        organizeId: organizeId.value,
        Token: token.value,
        aedtType: type.value === 1 ? 'quality' : 'security'
      }
  )
  if (res.data.Ret === 1) {
   const typeData = res.data.Data.List
    typeOption = [
      { text: '全部分类', value: '' }
    ]
    for (const typeDatum of typeData) {
      typeOption.push({
        text: typeDatum.AedtName,
        value: typeDatum.AedtGuid
      })
    }
    typeKey.value += 1
  }
}
/**
 * 跳转路由到新建页面
 */
function addQuality() {
router.push({
  path:'/addMission',
  query: {
    type: 1
  }
})
}
/**
 * 跳转路由到详情页面
 */
function gotoDetails(item) {
  router.push({
    path:'/addMission',
    query: {
      type: 1,
      examineId: item.ExamineID,
      createId: item.ExaminerID
    }
  })
}
/**
 * 删除单条数据
 * @param id
 * @returns {Promise<void>}
 */
async function deleteItem(id) {
  const temp = []
  temp.push(id)
  const res = await api.RemoveItems({
    Token: token.value,
    Ids: temp
  })
  if (res.data.Ret === 1){
    showToast(res.data.Msg)
    await getList()
  }else {
    showToast(res.data.Msg)
  }
}

/**
 * 关闭单条数据
 * @param id
 * @returns {Promise<void>}
 */
async function closeItem(id) {
  const temp = []
  temp.push(id)
  const res = await api.UpdateItems({
    Token: token.value,
    Ids: temp
  })
  if (res.data.Ret === 1){
    showToast(res.data.Msg)
    await getList()
  }else {
    showToast(res.data.Msg)
  }
}
const token = ref('')
let isBusiness = ref(false)
let isApp = ref(false)

onMounted(()=>{
  isBusiness = window.IP_CONFIG.IS_BUSINESS
  if (window.IP_CONFIG.IS_BUSINESS){
    let outerToken
    if (window.IP_CONFIG.IS_QUERY){
      const route = useRoute()
      outerToken = route.query.Token
    }else {
      // 获取外部token
      outerToken = location.search
      if (outerToken && outerToken.length > 7){
        outerToken = outerToken.slice(7)
      }else {
        outerToken = sessionStorage.getItem('token')
      }
    }
    // 获取BIMe-->Token
    getOuterToken(outerToken)
  }else {
    isApp.value = true
    const route = useRoute()
    const mToken = route.query.token
    if (!mToken) {
      token.value = getToken()
      userId = JSON.parse(getUserInfo()).UserId
      console.log('路由地址Token为空')
    } else {
      token.value = mToken
      setToken(token.value)
      getBIMeUserInfo()
    }
    const mOrganizeId = route.query.organizeId
    if (!mOrganizeId) {
      organizeId.value = getOrganizeId()
      console.log('路由地址项目Id为空')
    } else {
      organizeId.value = mOrganizeId
      setOrganizeId(mOrganizeId)
    }
    getList()
    getTypes()
  }
})
const projectOption  = ref([])
const organizeId = ref('')
/**
 * 获取项目列表
 * @returns {Promise<void>}
 */
async function getProjectList() {
  const res = await api.GetProjectList({
    token: token.value,
    pageNum: 1,
    pageSize: 99999,
    keyword: ''
  })
  if (res.data.Ret === 1) {
    const projectList = res.data.Data.rows
    if (projectList.length > 0){
      for (const projectListElement of projectList) {
        projectOption.value.push(
            {
              text: projectListElement.ProjectName,
              value: projectListElement.ProjectId
            }
        )
      }
      if (getOrganizeId()){
        organizeId.value = getOrganizeId()
        // 不为空
      }else {
        organizeId.value = projectOption.value[0].value
        setOrganizeId(organizeId.value)
      }
      // await getList()
      // await getTypes()
    }
  }
}
/**
 * 获取外部token
 * @returns {Promise<void>}
 */
async function getOuterToken(outerToken) {
  const res = await api.LongyunLogin(outerToken)
  if (res.data.Ret === 1){
    token.value = res.data.Data.token
    setToken(token.value)
    await getProjectList()
    await getBIMeUserInfo()
  }else {
    showToast(res.data.Msg)
  }
  console.log('获取token',res)
}
let userId = ref('')
/**
 * 获取用户信息并保存
 */
async function getBIMeUserInfo() {
  const res = await api.GetUser({
    token: token.value
  })
  if (res.data.Ret === 1){
    userId = res.data.Data.UserId
    setUserInfo(JSON.stringify(res.data.Data))
  }
}
watch(statusValue, async (newValue,oldValue) =>{
  await getList()
})
watch(typeValue, async (newValue,oldValue) =>{
  await getList()
})
watch(classValue, async (newValue,oldValue) =>{
  await getList()
})
watch(organizeId, async (newValue,oldValue) =>{
  setOrganizeId(newValue)
  await getList()
  await getTypes()
})
</script>

<style scoped lang="scss">
.list-container{
  background-color: #f2f2f2;
  .item-wrapper{
    display: flex;
    flex-direction: column;
    border-radius: 4px;
    background-color: white;
    margin: 16px;
    .top-wrapper{
      display: flex;
      padding: 6px;
      .name-bg{
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #283A4F;
        width: 40px;
        height: 40px;
        border-radius: 360px;
        span{
          font-size: 14px;
          color: white;
        }
      }
      .right-wrapper{
        margin-left: 8px;
        display: flex;
        flex-direction: column;
        font-size: 14px;
        font-weight: 500;
        .wrapper-creat{
          margin-top: 8px;
        }
      }
      .status-wrapper{
        font-size: 13px;
        color: white;
        position: absolute;
        right: 0;
        .status-toBeCheck{
          padding: 3px 10px 3px 10px;
          background-color: #FF9C00;
          border-radius: 16px 0 0 16px;
        }
        .status-toBeRectified{
          padding: 3px 10px 3px 10px;
          background-color: #FF464F;
          border-radius: 16px 0 0 16px;
        }
        .status-toBeRecheck{
          padding: 3px 10px 3px 10px;
          background-color: #1974FF;
          border-radius: 16px 0 0 16px;
        }
        .status-qualified{
          padding: 3px 10px 3px 10px;
          background-color: #11B64F;
          border-radius: 16px 0 0 16px;
        }
        .status-closed{
          padding: 3px 10px 3px 10px;
          background-color: #99A7C2;
          border-radius: 16px 0 0 16px;
        }
      }
    }
    .center-wrapper{
      display: flex;
      align-items: center;
      margin-top: 15px;
      span{
        font-weight: 500;
        margin-left: 6px;
        font-size: 14px;
      }
      span:first-of-type{
        margin-left: 6px;
        color: white;
        font-size: 10px;
        border-radius: 2px;
        background-color: #007AFF;
        padding: 3px;
      }
    }
    .bottom-wrapper{
      margin-top: 15px;
      margin-bottom: 10px;
      span{
        margin-left: 6px;
        font-size: 13px;
        color: #616F7D;
      }
      span:first-of-type{
        color: #A6AEB6;
      }
    }
    .delete-button {
      height: 100%;
    }
    .item-right-wrapper{
      height: 100%;
      img{
        width: auto;
        height: auto;
        max-width: 100%;
        max-height: 100%;

      }
    }
  }
  .van-image{
    position: fixed;
    bottom: 100px;
    right: 16px;
  }
}

</style>
/**
 * 质量和安全
 */

 import { request } from '@/utils/request'
 export default {
  GetUserPaged: params => request({url:'/api/User/User/GetUserPaged',params,isToken:false}),
  GetExamTypes: params => request({url:'/api/Examine/Exam/Exam_GetExamTypes',params,isToken:false}),
  UploadImages: params => request({ url: '/api/Tool/File/UploadImages', method: 'post', params, headers: { 'Content-Type': 'multipart/form-data' },isToken:false}),
  GetCategories: params => request({url:'/api/Material/MaterialCategory/GetCategories',params,isToken:false}),
  GetCategoriesItem: params => request({url:'/api/Material/Mtr/GetMaterialList_Condition2',params,isToken:false}),
  GetPlanList: params => request({url:'/api/v1/plus/GetList',params,baseURL:'TASK_URL',isToken:false}),
  GetPlanItemList: params => request({url:'/api/v1/plus/GetPlanTaskListByPlanId',params,baseURL:'TASK_URL',isToken:false}),
  GetMissions:params => request({url:'/api/Examine/Exam/GetMissions',params,isToken:false}),
  AddMission: params => request({ url: '/api/Examine/Exam/AddMission', method: 'post', params,isToken:false}),
  RemoveItems: params => request({url: '/api/Examine/Exam/RemoveItems', method: 'post', params,isToken:false}),
  UpdateItems: params => request({url: '/api/Examine/Exam/UpdateItems', method: 'post', params,isToken:false}),
  CheckMission: params => request({ url: '/api/Examine/Exam/CheckMission', method: 'post', params,headers: { 'Content-Type': 'multipart/form-data' },isToken:false}),
  GetMission: params => request({url:'/api/Examine/Exam/GetMission',params,isToken:false}),
  ApplyToAcceptance: params => request({url: '/api/Examine/Exam/ApplyToAcceptance', method: 'post', params,headers: { 'Content-Type': 'multipart/form-data' },isToken:false}),
  TryToAcceptance: params => request({url: '/api/Examine/Exam/TryToAcceptance', method: 'post', params,headers: { 'Content-Type': 'multipart/form-data' },isToken:false}),
  ModifyMissionInfo: params => request({url: '/api/Examine/Exam/ModifyMissionInfo', method: 'post', params,isToken:false}),
  ModifyMissionChecker: params => request({url: '/api/Examine/Exam/ModifyMissionChecker', method: 'post', params,isToken:false})






 }
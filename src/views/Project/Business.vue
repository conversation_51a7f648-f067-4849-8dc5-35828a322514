<template>
  <div class="business-container">
    <van-sticky>
      <van-nav-bar
          :title="getProjectName()"
          left-text="返回"
          left-arrow
          @click-left="router.push('/home')"
      />
    </van-sticky>
    <div class="item" @click="goToRouter(0)">
      <img class="legend" src="../../assets/images/business/business-quality.png" alt="">
      <span>质量巡查</span>
      <img class="right-arrow" src="../../assets/images/business/business-right-arrow.png" alt="">
    </div>
    <div class="item" @click="goToRouter(1)">
      <img class="legend" src="../../assets/images/business/business-safe.png" alt="">
      <span>安全巡查</span>
      <img class="right-arrow" src="../../assets/images/business/business-right-arrow.png" alt="">
    </div>
    <div class="item" @click="goToRouter(2)">
      <img class="legend" src="../../assets/images/business/business-schedule.png" alt="">
      <span>进度填报</span>
      <img class="right-arrow" src="../../assets/images/business/business-right-arrow.png" alt="">
    </div>
    <div class="item" @click="goToRouter(3)">
      <img class="legend" src="../../assets/images/business/business-flow.png" alt="">
      <span>业务流程</span>
      <img class="right-arrow" src="../../assets/images/business/business-right-arrow.png" alt="">
    </div>
  </div>

  <router-view></router-view>
</template>
<script setup>
import router from "@/router";
import {getProjectName} from "@/utils/projectName";

function goToRouter(type) {
  switch (type) {
    case 0:
      router.push('/project/quality')
      break
    case 1:
      router.push('/project/safe')
      break
    case 2:
      router.push('/project/schedule')
      break
    case 3:
      router.push('/project/flow')
      break
  }
}
</script>

<style scoped lang="scss">
.business-container{
  height: 100%;
  overflow: auto;
  .item{
    height: 120px;
    margin: 20px;
    display: flex;
    align-items: center;
    position: relative;
    background: linear-gradient( 270deg, rgba(255,255,255,0.1) 0%, #FAFDFF 100%);
    box-shadow: 0px 2px 4px 0px rgba(0,0,0,0.1);
    border-radius: 8px;
    .legend{
      margin-left: 17px;
      width: 90px;
      height: 90px;
    }
    span{
      margin-left: auto;
      font-weight: 500;
      font-size: 18px;
      color: #2D2F32;
    }
    .right-arrow{
      margin-right: 17px;
      margin-left: auto;
      width: 20px;
      height: 20px;
    }
  }
}

</style>
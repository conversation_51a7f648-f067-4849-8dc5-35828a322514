<template>
  <div class="message-container">
    <van-sticky>
      <van-nav-bar
          title="消息列表"
          left-text="返回"
          left-arrow
          @click-left="onClickLeft"
      />
      <van-tabs v-model:active="active" @click-tab="onClickTab" sticky animated>
        <van-tab title="待办" name="todoList"></van-tab>
        <van-tab title="全部" name="allList"></van-tab>
      </van-tabs>
    </van-sticky>
    <div class="list-container">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list
            v-model:loading="loading"
            :finished="finished"
            finished-text=""
        >
          <van-checkbox-group v-model="messageValue">
            <div v-for="(item,index) in listData" :key="item.id" @click="gotoDetails(item)">
              <div class="item-wrapper">
                <van-checkbox
                    :disabled="item.HasRead"
                    class="left-wrapper"
                    :name="item"
                    :ref="el => checkboxMsgRefs[index] = el"
                    @click.stop>
                </van-checkbox>
                <div class="right-wrapper">
                  <div class="top-wrapper">
                    <van-image
                        height="16"
                        width="16"
                        :src="switchImage(item.Module)"
                    />
                    <div class="type-name">
                      <span>{{item.Module}}</span>
                      <div v-if="!item.HasRead" class="red-dot"></div>
                    </div>
                  </div>
                  <div class="center-wrapper">
                    <span>{{item.Content}}</span>
                  </div>
                  <div class="bottom-wrapper">
                    <span>{{item.CreateTime}}</span>
                  </div>
                </div>
              </div>
            </div>
          </van-checkbox-group>
        </van-list>
      </van-pull-refresh>
    </div>
    <van-sticky :offset-bottom="-30" position="bottom" v-if="messageValue.length > 0">
      <van-button
          class="add-btn" type='primary'
          @click="submit">设为已完成</van-button>
    </van-sticky>
  </div>
</template>
<script setup>
import router from "@/router";
import {onMounted, ref, watch} from "vue";
import api from "@/api";
import {getToken} from "@/utils/token";
import {getOrganizeId} from "@/utils/organizeId";
import {setCCToken} from "@/utils/cctoken";

/**
 * 移动端监听关闭按钮
 */
function onClickLeft(){
  router.back()
 /* if (navigator.userAgent.indexOf('Android') > -1){
    mjs.clickBack()
  }else if (navigator.userAgent.indexOf('iPhone') > -1){
    window.webkit.messageHandlers.clickBack.postMessage(null)
  }else {
    router.back()
  }*/
}
const active = ref(0)
const listType = ref('')
const messageValue =ref([])
const checkboxMsgRefs = ref([]);

/**
 * tab点击切换
 * @param name
 */
const onClickTab = ({ name }) => {
  listType.value = name
  listData.value = []
  getList()
};

let loading = ref(false);
const finished = ref(false);
const refreshing = ref(false);
/**
 * 刷新列表
 */
function onRefresh() {
  listData.value = []
  // 清空列表数据
  finished.value = false;
  // 重新加载数据
  // 将 loading 设置为 true，表示处于加载状态
  loading.value = true;
  // 加载数据
  getList();
  // 取消刷新状态
  refreshing.value = false
}

const listData = ref([])
/**
 * 获取消息列表
 */
async function getList() {
  if (listData.value.length > 0 ){
    listData.value = []
  }
  let params
  if (listType.value === 'allList'){
    params = {
      Module: '',
      Token: getToken()
    }
  }else {
    params = {
      HasRead: false,
      Module: '',
      Token: getToken()
    }
  }
  const result = await api.ListApp(params)
  if (result.data.Ret === 1){
    listData.value = result.data.Data
  }
  finished.value = true
  loading.value = false
}

/**
 * 设置消息已读
 */
async function submit() {
  let ids = []
  for (const resultElement of messageValue.value) {
    ids.push(resultElement.MessageId)
  }
  const result = await api.Read(getToken(), ids)
  if (result.data.Ret === 1) {
    messageValue.value = []
    await getList()
  }
}

/**
 * 获取详情跳转
 * @param item
 */
function gotoDetails(item) {
  switch (item.Module) {
    case '进度':
      router.push({
        path: '/scheduleDetails',
        query: {
          id: item.ObjectId,
        }
      })
      break
    case '问题':

      break
    case '质量':
      router.push({
        path:'/addMission',
        query: {
          type: 1,
          examineId: item.ObjectId,
          createId: item.ExaminerID,
          projectId: item.ProjectId
        }
      })
      break
    case '安全':
      router.push({
        path:'/addMission',
        query: {
          type: 2,
          examineId: item.ObjectId,
          createId: item.ExaminerID,
          projectId: item.ProjectId
        }
      })
      break
    case '流程':
      getCCT(item)
      break
  }
  console.log(item)
}

/**
 * 选择消息图标
 * @param type
 */
function switchImage(type) {
  let urlSrc
  switch (type){
    case '进度':
      urlSrc = require('../../assets/images/message/message-scheduale.png')
      break
    case '问题':
      urlSrc = require('../../assets/images/message/message-issue.png')
      break
    case '质量':
      urlSrc = require('../../assets/images/message/message-quality.png')
      break
    case '安全':
      urlSrc = require('../../assets/images/message/message-safe.png')
      break
    case '流程':
      urlSrc = require('../../assets/images/message/message-flow.png')
      break
    default:
      urlSrc = require('../../assets/images/common/message-icon.png')
      break

  }
  return urlSrc
}
/**
 * 获取流程信息
 */
async function getFlowInfo(item) {
  const result = await api.GetWorkFlowInfoByWorkId({
    Token: getToken(),
    objectId: item.ObjectId
  })
  if (result.data.Ret === 1){
    router.push({
      path:'/flowDetails',
      state:{
        data: JSON.stringify(result.data.Data)
      }
    })
  }
}
/**
 * 获取流程token
 * @returns {Promise<void>}
 */
async function getCCT(item) {
  const res = await api.CCFlowLogin({
    Token: getToken(),
    organizeId: item.ProjectId
  })
  if (res.data.Ret === 1) {
    setCCToken(res.data.Data)
    await getFlowInfo(item)
  }
}
onMounted(()=>{
  getList()
})
</script>
<style scoped lang="scss">
.message-container{
  background-color: #f2f2f2;
  .list-container{
    height: calc(100% - 100px);
    overflow: auto;
    margin: 16px;
    .item-wrapper{
      height: 120px;
      display: flex;
      margin-bottom: 16px;
      border-radius: 4px;
      background-color: white;
      .left-wrapper{
        margin-left: 10px;
        padding-right: 10px;
      }
      .right-wrapper{
        width: 300px;
        display: flex;
        flex-direction: column;
        margin-right: 10px;
        .top-wrapper{
          display: flex;
          align-items: center;
          margin-top: 14px;
          .type-name{
            position: relative;
            span{
              font-weight: 500;
              font-size: 14px;
              margin-left: 12px;
            }
            .red-dot::before {
              content: "";
              position: absolute;
              top: -5px;
              right: -5px;
              width: 5px;
              height: 5px;
              background-color: #f00;
              border-radius: 50%;
            }
          }
        }
        .center-wrapper{
          margin-top: 10px;
          span{
            font-size: 13px;
            font-weight: 400;
            overflow:hidden;
            text-overflow:ellipsis;
            display:-webkit-box;
            -webkit-box-orient:vertical;
            -webkit-line-clamp:2;
          }
        }
        .bottom-wrapper{
          margin-top: 10px;
          span{
            font-size: 12px;
            color: #666666;
            font-weight: 400;
          }
        }
      }


    }
  }
  .add-btn{
    width: calc(100% - 20px);
    margin: 32px 10px 32px 10px
  }
}
</style>
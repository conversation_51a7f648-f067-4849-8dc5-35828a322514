/**
 * 组件相关接口
 */
 import { request } from '@/utils/request'
 export default {
  Login: params => request({ url: '/api/User/Home/Login', method: 'post', params,isToken:false,isLoop: true }),
  GetProjectList: params => request({url:'/api/User/Project/Paged',params,isToken:false}),
  GetUserObjectByUserId: params => request({url:'/api/User/Home/GetUserObjectByUserId',params,isToken:false}),
  ModifyUserInfo: params => request({url: '/api/User/User/ModifyUserInfo', method: 'post', params,isToken:false}),
  GetUser: params => request({url:'/api/User/Home/GetUser',params}),
  GetProjectOrg: params => request({ url: '/api/User/User/GetTokenComInfo', params, isLoop: true }), 
  GetMain: params => request({ url: '/api/report/investment/main', params, isLoop: true }),
  GetMainDetail: params => request({ url: '/api/report/investment/main-detail', params, isLoop: true }),
  GetQuarterchat: params => request({ url: '/api/report/investment/quarterchat', params, isLoop: true }),
  GetProgressLeft: params => request({ url: '/api/report/progress/left', params, isLoop: true }),
  GetProgressRight: params => request({ url: '/api/report/progress/right', params, isLoop: true }),
  GetQuanlityleft: params => request({ url: '/api/report/quanlity/quanlity-left', params, isLoop: true }),
  GetQuanlityRight: params => request({ url: '/api/report/quanlity/quanlity-right', params, isLoop: true }),
  GetMaterialtestingleft: params => request({ url: '/api/report/quanlity/materialtesting-left', params, isLoop: true }),
  GetMaterialtestingRight: params => request({ url: '/api/report/quanlity/materialtesting-right', params, isLoop: true }),
  GetSummary: params => request({ url: '/api/report/quanlity/summary', params, isLoop: true }),
  GetList: params => request({ url: '/api/report/quanlity/list', params, isLoop: true }),
  GetHazardLeft: params => request({ url: '/api/v1/safety/hazard-left', params, isLoop: true }),
  GetHazardRight: params => request({ url: '/api/v1/safety/hazard-right', params, isLoop: true }),
  GetBlackspotLeft: params => request({ url: '/api/v1/safety/blackspot-left', params, isLoop: true }),
  GetblackspotRight: params => request({ url: '/api/v1/safety/blackspot-right', params, isLoop: true }),
  GetUrlsByCod: params => request({ url: '/api/UserControllers/Cfg/GetUrlsByCode', params,baseURL:'GET_COMPANY_CODE_URL', isToken:false, isLoop: false }),


 }
<template>
    <div class="echarts-content">
        <div
            class="echart"
            :id="echartsID"
            :options="echartsOption"
        ></div>
    </div>
</template>
<script setup>
import * as echarts from 'echarts';
import {onMounted, watch, defineProps, nextTick} from 'vue'
const props = defineProps({
  echartsID: String,
  echartsOption: Object,
  changeData: Number
})
let echartsElement
const echartsLoad = async() => {
    await nextTick()
    echartsElement = echarts.init(
        document.getElementById(props.echartsID)
    );
    let option = props.echartsOption;
    echartsElement.setOption(option);
    // echartsElement.resize()
}
watch(
  () => props.changeData,
  (changeData) => {
    console.log(changeData)
    echartsElement?.setOption(props.echartsOption);
  },
  { deep: true }
)
onMounted(()=>{
    echartsLoad()
    window.addEventListener("resize", () => {
        // 判断是否存在，直接调用resize方法
        if (echartsElement) echartsElement.resize()
    });
})
</script>
<style lang="scss" scoped>
.echarts-content {
    width: 100%;
    height: 100%;
    .echart{
        width: 100%;
        height: 100%;
    }
}
</style>
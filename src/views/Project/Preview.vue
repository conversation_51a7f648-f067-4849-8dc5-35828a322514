<template>
  <div class="preview-container" v-if="visible">
    <van-sticky>
      <van-nav-bar
          title="文件在线预览"
          left-text="返回"
          left-arrow
          @click-left="onClickLeft"
      />
    </van-sticky>
    <iframe class="ifr" :src="props.url" frameborder="0"></iframe>
  </div>
</template>
<script setup>

import { defineProps } from 'vue'
const props = defineProps({
  visible:{
    type: Boolean,
    default: false
  },
  url: {
    type: String,
  },
  isShow:{
    type: Boolean,
    default: false
  }
})
import { defineEmits } from 'vue'
const emit = defineEmits(['closeDialog'])
function onClickLeft() {
  emit('closeDialog')
}
</script>

<style scoped lang="scss">
.preview-container{
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(29, 29, 29, 0.27);
  width: 100%;
  height: calc(100% - 47px);
  z-index: 999;
  .ifr{
    width: 100%;
    height: 100%;
  }
}
</style>
<template>
  <div class="list-container">
    <van-sticky>
      <van-nav-bar
        :title="getProjectName()"
        left-text="返回"
        left-arrow
        @click-left="onClickLeft"
      />
      <van-dropdown-menu>
        <van-dropdown-item v-model="statusValue" :options="statusOption" />
        <van-dropdown-item v-model="typeValue" :options="typeOption" />
        <van-dropdown-item v-model="archiveValue" :options="archiveOption" />
      </van-dropdown-menu>
    </van-sticky>
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
      <van-list
        class="list"
        v-model:loading="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="onLoad">
        <div v-for="item in listData" :key="item.ExamineID" @click="gotoDetails(item)">
          <van-swipe-cell>
            <div class="item-wrapper">
              <div class="left-wrapper">
                <van-image
                  class="left-img"
                  :src="getImageUrl(item)"
                />
              </div>
              <div class="right-wrapper">
                <div class="name">{{item.Title}}</div>
                <div class="time"><van-icon name="manager-o" color="#616F7D" class="mr-10" />{{item.CreateUserName}}</div>
                <div class="time">{{'采集时间: ' + item.CreateDate.split(' ')[0]}}</div>
              </div>
            </div>
            <template #right v-if="item.isIssueManager">
              <div class="item-right-wrapper">
                <img :src="require('../../assets/images/quality/delete-item.png')" alt="" @click="deleteItem(item.IssueId)">
              </div>
            </template>
          </van-swipe-cell>
        </div>
      </van-list>
    </van-pull-refresh>
    <van-image
      class="add-img"
      width="60"
      height="60"
      :src="require('../../assets/images/common/add-btn.png')"
      @click="addPano"
    ></van-image>
  </div>

</template>
<script setup>
import router from "@/router";
import {onMounted, ref, watch} from "vue";
import api from "@/api";
import {getOrganizeId} from "@/utils/organizeId";
import {getOrganizationId} from "@/utils/OrganizationId";
import {showToast} from "vant";
import {getToken} from "@/utils/token";
import {getProjectName} from "@/utils/projectName";
const token = ref('')
const organizeId = ref('')
const organizationId = ref('')
const statusValue = ref('');
const typeValue = ref('');
const archiveValue = ref('0');
let typeOption = [
  { text: '全部类型', value: '' }
];
let statusOption = [
  { text: '全部状态', value: '' }
];
let archiveOption = [
  { text: '归档/隐', value: '0' },
  { text: '归档/显', value: '1' }
];
let loading = ref(false);
const finished = ref(false);
const refreshing = ref(false);
const listData = ref([])
const searchValue = ref('');
/**
 * 刷新列表
 */
function onRefresh() {
  // 清空列表数据
  finished.value = false;
  // 重新加载数据
  // 将 loading 设置为 true，表示处于加载状态
  loading.value = true;
  // 加载数据
  getList();
  // 取消刷新状态
  refreshing.value = false
}
/**
 * 左侧点击回退上一级路由
 * 注：replace 方法不会增加路由信息
 */
function onClickLeft(){
  router.push('/home')
}
/***
 * 获取图片地址
 */
function getImageUrl(item) {
  if (item.ViewpointID === null || item.ViewpointID === '') {
    return item.bgpicture_src
  }else {
    return item.ImageUrl
  }
}
/**
 * 列表加载方法
 */
function onLoad(){
}
/**
 * 获取协作数据
 */
async function getList() {
  loading.value = true
  const res = await api.GetIssueList({
    Token: token.value,
    OrganizeId: organizeId.value,
    IssueStatusId: statusValue.value,
    IssueTypeId: typeValue.value,
    ContainsArchive: archiveValue.value,
    Keyword: searchValue.value,
    UserType: '',
    PageNum: 1,
    PageSize: 999
  })
  if (res.data.Ret === 1){
    listData.value = res.data.Data.Items
  }else (
    listData.value = []
  )
  finished.value = true
  loading.value = false
}

/**
 * 获取状态和类型
 * @returns {Promise<void>}
 */
async function getTypesAndStatus(){
  const res = await api.GetIssueStatus({
    token: token.value,
    organizeId: organizationId.value
  })
  if (res.data.Ret === 1) {
   const statusData = res.data.Data
    statusOption = [
      { text: '全部状态', value: '' }
    ]
    for (const label of statusData) {
      statusOption.push({
        text: label.ItemName,
        value: label.ItemDetailId
      })
    }
  }
  const res1 = await api.GetIssueTypes({
    token: token.value,
    organizeId: organizationId.value
  })
  if (res1.data.Ret === 1) {
   const typeData = res1.data.Data
    typeOption = [
      { text: '全部类型', value: '' }
    ]
    for (const label of typeData) {
      typeOption.push({
        text: label.ItemName,
        value: label.ItemDetailId
      })
    }
  }
}
/**
 * 跳转路由到新建页面
 */
function addPano() {
  router.push({
    path:'/addIssue'
  })
}
/**
 * 路由跳转预览页面
 */
function gotoDetails(item) {
  router.push({
    path:'/addIssue',
    query: {
      issueId: item.IssueId
    }
  })
}
/**
 * 删除单条数据
 * @param id
 * @returns {Promise<void>}
 */
async function deleteItem(IssueId) {
  const res = await api.RemoveIssue({
    token: token.value,
    IssueId: IssueId
  })
  if (res.data.Ret === 1){
    showToast(res.data.Msg)
    await getList()
  }else {
    showToast(res.data.Msg)
  }
}
onMounted(()=>{
  token.value = getToken()
  organizationId.value = getOrganizationId()
  organizeId.value = getOrganizeId()
  getList()
  getTypesAndStatus()
})
watch(typeValue, async (newValue,oldValue) =>{
  await getList()
})
watch(statusValue, async (newValue,oldValue) =>{
  await getList()
})
watch(archiveValue, async (newValue,oldValue) =>{
  await getList()
})
</script>

<style scoped lang="scss">
.list-container{
  height: 100%;
  background-color: #f2f2f2;
  ::-webkit-scrollbar{
    display: none;
  }
  .list{
    padding: 16px;
    overflow-y: scroll;
    margin-bottom: 48px;
    .item-wrapper{
      display: flex;
      margin-bottom: 16px;
      padding: 12px;
      border-radius: 4px;
      background-color: white;
      .left-wrapper{
        width: 118px;
        height: 88px;
        border-radius: 4px;
        .left-img{
          width: 100%;
          height: 100%;
        }
      }
      .right-wrapper{
        margin-left: 8px;
        width: calc(100% - 126px);
        .name{
          width: 100%;
          height: 22px;
          font-size: 16px;
          font-weight: 500;
          color: #323233;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .time{
          width: 100%;
          height: 22px;
          margin-top: 10px;
          font-size: 16px;
          font-weight: 400;
          color: #7a7a7d;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
    .item-right-wrapper{
      height: 100%;
      img{
        width: auto;
        height: auto;
        max-width: 100%;
        max-height: 100%;

      }
    }
  }
  .add-img{
    position: fixed;
    bottom: 100px;
    right: 16px;
  }
}

</style>
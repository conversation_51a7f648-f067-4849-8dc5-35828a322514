/**
 * 文档
 */

import { request } from '@/utils/request'
import {getToken} from "@/utils/token";
export default {
    GetDocumentTree: params => request({url: '/api/v1/folder/tree', params,isToken: false}),
    GetDocumentList: params => request({url: '/api/v1/folder/file-folder', params}),
    GetDocumentAuth: params => request({url: '/api/v1/folder/folder-auth', params,isToken: false}),
    SearchDocument: params => request({url: '/api/v1/file/search', params,isToken: false}),
    DeleteDocument: params => request({url: `/api/v1/file/delete-batch?Token=${getToken()}`, method: 'post',params,isToken: false}),


}
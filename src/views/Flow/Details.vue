<template>
  <div class="details-container" ref="detailsContainer">
    <van-sticky>
      <van-nav-bar
          :title="flowData.Title"
          left-text="返回"
          left-arrow
          @click-left="onClickLeft"
      />
    </van-sticky>
    <div class="iframe-container">
    <iframe src=""
            width="100%"
            height="100%"
            allowfullscreen
            id="iframe"
    />
    </div>

  </div>
</template>
<script setup>
import router from "@/router";
import {onMounted, onUnmounted, ref} from "vue";
import {getCCToken} from "@/utils/cctoken";
import {getUserInfo} from "@/utils/user";
const flowData = ref('')
const detailsContainer = ref()
/**
 * 旋转屏幕
 */
function resizeScreen() {
  // 利用 CSS3 旋转 对根容器逆时针旋转 90 度
  const detectOrient = function() {
    let width = document.documentElement.clientWidth,
        height = document.documentElement.clientHeight,
        wrapper = detailsContainer.value, // 这里是页面最外层元素
        style = "";
    if (width >= height) {
      console.log("横屏!!!!!")
      // 横屏
      style += "width:" + width + "px;"; // 注意旋转后的宽高切换
      style += "height:" + height + "px;";
      style += "-webkit-transform: rotate(0); transform: rotate(0);";
      style += "-webkit-transform-origin: 0 0;";
      style += "transform-origin: 0 0;";
    } else {
      console.log("竖屏!!!!!")

      // 竖屏
      style += "width:" + height + "px;";
      style += "height:" + width + "px;";
      style +=
          "-webkit-transform: rotate(90deg); transform: rotate(90deg);";
      // 注意旋转中点的处理
      style +=
          "-webkit-transform-origin: " +
          width / 2 +
          "px " +
          width / 2 +
          "px;";
      style += "transform-origin: " + width / 2 + "px " + width / 2 + "px;";
    }
    console.log("style",style)
    wrapper.style.cssText = style;
  };
  window.onresize = detectOrient;
  detectOrient();
}
/**
 * 开始旋转屏幕
 */
function rotateScreen(){
  // 由于对象映射，所以调用android对象等于调用Android映射的对象
  android.rotateScreen();
}
/**
 * 取消旋转屏幕
 */
function cancelRotateScreen(){
  // 由于对象映射，所以调用android对象等于调用Android映射的对象
  android.cancelRotateScreen();
}

onMounted(()=>{
  // rotateScreen()
  // resizeScreen()
  // 获取参数
  let data = window.history.state.data;
  let  atPara
  if (data){
    flowData.value = JSON.parse(data)
    if (flowData.value.AtPara){
      atPara =  flowData.value.AtPara.replaceAll('@','&')
    }
  }
  const userInfo = JSON.parse(getUserInfo())
  const account = userInfo.Account
  const ccToken = getCCToken()
  //http://workflow-api.probim.cn/WF/MyFlowGener.htm?WorkID=1040&NodeID=6502&FK_Node=6502&FID=0&UserNo=lfd001&Token=&FK_Flow=065&PWorkID=0&IsRead=1&Paras=1&IsCanBatch=0&LastTruckID=*********&ThreadCount=0&ScripNodeID=0&ScripMsg=&HungupSta=-1&HungupCheckMsg=&PageName=MyFlowGener
  //http://workflow-api.probim.cn/WF/MyFlowGener.htm?WorkID=1040&NodeID=6502&FK_Node=6502&FID=0&UserNo=lfd001&Token=&FK_Flow=065&PWorkID=0&IsRead=1&Paras=1&IsCanBatch=0&LastTruckID=*********&ThreadCount=0&ScripNodeID=0&ScripMsg=&HungupSta=-1&HungupCheckMsg=&PageName=MyFlowGener
  let iframeSrc =  window.IP_CONFIG.FLOW_URL+ `/WF/MyFlowGener.htm?WorkID=${flowData.value.WorkID}&NodeID=${flowData.value.FK_Node}&FK_Node=${flowData.value.FK_Node}&FID=${flowData.value.FID}&UserNo=${account}&Token=${ccToken}&FK_Flow=${flowData.value.FK_Flow}&PWorkID=${flowData.value.PWorkID}&IsRead=${flowData.value.IsRead}&Paras=1`+atPara+'&PageName=MyFlowGener'
  console.log('src',iframeSrc)
  let iframe  = document.getElementById('iframe')
  iframe.src = iframeSrc
})
onUnmounted(()=>{
  // cancelRotateScreen()
})
/**
 * 点击返回上一级路由
 */
function onClickLeft() {
  // cancelRotateScreen()
  router.back()
}
</script>
<style scoped lang="scss">
.details-container{
  width: 100%;
  height: 100%;
    .iframe-container{
      width: 100%;
      height: 100%;
      iframe{
        overflow: scroll;
        width: 1300px;
        height: 1300px;
        border: 0;
        //transform-origin: left top;
        //transform: scale(0.535, 0.525)
      }
  }
}
</style>
/**
 * 根据 cu_urltype 获取 localStorage 中 companyInfo 数组对应对象的 cu_url
 * @param {string} cu_urltype - 要查找的 cu_urltype 值
 * @returns {string|null} 返回对应的 cu_url，如果未找到则返回 null
 */
export const getCompanyUrlByType = (cu_urltype) => {
  try {
    // 从 localStorage 获取 companyInfo
    const companyInfoStr = localStorage.getItem('companyInfo')
    
    // 检查是否存在 companyInfo
    if (!companyInfoStr) {
      console.warn('localStorage 中未找到 companyInfo')
      return null
    }
    
    // 解析 JSON 字符串
    const companyInfo = JSON.parse(companyInfoStr)
    
    // 检查是否为数组
    if (!Array.isArray(companyInfo)) {
      console.warn('companyInfo 不是一个数组')
      return null
    }
    
    // 查找匹配的对象
    const targetItem = companyInfo.find(item => item.cu_urltype === cu_urltype)
    
    // 返回对应的 cu_url
    return targetItem ? targetItem.cu_url : null
    
  } catch (error) {
    console.error('解析 companyInfo 时出错:', error)
    return null
  }
}

/**
 * 获取所有可用的 cu_urltype 列表
 * @returns {string[]} 返回所有 cu_urltype 的数组
 */
export const getAllCompanyUrlTypes = () => {
  try {
    const companyInfoStr = localStorage.getItem('companyInfo')
    
    if (!companyInfoStr) {
      return []
    }
    
    const companyInfo = JSON.parse(companyInfoStr)
    
    if (!Array.isArray(companyInfo)) {
      return []
    }
    
    return companyInfo.map(item => item.cu_urltype).filter(Boolean)
    
  } catch (error) {
    console.error('获取 cu_urltype 列表时出错:', error)
    return []
  }
}

/**
 * 获取完整的 companyInfo 数组
 * @returns {Array|null} 返回 companyInfo 数组，如果获取失败则返回 null
 */
export const getCompanyInfo = () => {
  try {
    const companyInfoStr = localStorage.getItem('companyInfo')
    
    if (!companyInfoStr) {
      return null
    }
    
    return JSON.parse(companyInfoStr)
    
  } catch (error) {
    console.error('获取 companyInfo 时出错:', error)
    return null
  }
}

/**
 * 检查指定的 cu_urltype 是否存在
 * @param {string} cu_urltype - 要检查的 cu_urltype 值
 * @returns {boolean} 如果存在返回 true，否则返回 false
 */
export const hasCompanyUrlType = (cu_urltype) => {
  try {
    const companyInfoStr = localStorage.getItem('companyInfo')
    
    if (!companyInfoStr) {
      return false
    }
    
    const companyInfo = JSON.parse(companyInfoStr)
    
    if (!Array.isArray(companyInfo)) {
      return false
    }
    
    return companyInfo.some(item => item.cu_urltype === cu_urltype)
    
  } catch (error) {
    console.error('检查 cu_urltype 时出错:', error)
    return false
  }
}

<template>
<div class="login-container">
  <span class="title">欢迎登录</span>
  <van-form>

    <van-cell-group inset class="cell-group">
      <van-cell center>
        <van-field
            v-model="userName"
            name="用户名/手机号"
            placeholder="用户名"
            :rules="[{ required: true, message: '请填写用户名' }]"
        />
        <template #icon>
          <van-image width="24" height="24" :src="require('../../assets/images/login/account.png')"/>
        </template>
        <template #right-icon>
          <van-image width="24" height="24" :src="require('../../assets/images/login/close.png')" @click="clearName"/>
        </template>
      </van-cell>

    </van-cell-group>

    <van-cell-group inset class="cell-group">
      <van-cell center>
        <van-field
            v-model="password"
            :type="fieldType"
            name="密码"
            placeholder="密码"
            :rules="[{ required: true, message: '请填写密码' }]"/>
        <template #icon>
          <van-image width="24" height="24" :src="require('../../assets/images/login/password.png')" />
        </template>
        <template #right-icon>
          <van-image width="24" height="24" :src="require('../../assets/images/login/invisible.png')" @click="togglePasswordVisibility"/>
        </template>
      </van-cell>
    </van-cell-group>
    <div class="remember-wrapper">
      <van-checkbox v-model="rememberChecked" shape="square" icon-size="14px">记住密码</van-checkbox>

    </div>
    <div class="btn-wrapper">
      <van-button round block type="primary" @click="login">
        登录
      </van-button>
    </div>
  </van-form>
  <div class="agreement-wrapper">
    <div class="top">
      <span>登录代表同意</span>
      <span @click="goToAgreement">《BIMe用户协议》和《隐私政策》</span>
    </div>
    <span class="ip">京ICP备14047424号-4A</span>
  </div>
</div>
</template>
<script setup>
import {nextTick, onMounted, ref, watch} from "vue";
import api from "@/api";
import CryptoJS from 'crypto-js'
import { setToken} from "@/utils/token";
import {showToast} from "vant";
import router from "@/router";
import {setUserInfo} from "@/utils/user";
import {getRemember, removeRemember, setRemember} from "@/utils/remember";
import {setOrganizeId} from "@/utils/organizeId";
import {setTabIndex} from "@/utils/tabIndex";
import {useStore} from "vuex";
import {useRoute} from "vue-router";
import {getIsAutoLogin, setIsAutoLogin} from "@/utils/isAutoLogin";

const rememberChecked = ref(false)
const userName = ref('');
const password = ref('');
const business = ref('')
const customBusinessCode = ref('')
const fieldType = ref('password')
const isShowSelectCode = ref(false)
const store = useStore()
const isCustomDone = ref(false)
const customLogo = ref(require('../../assets/images/login/default-ogo.png'))
const customName = ref('')

onMounted(() => {
  business.value = store.state.code.companyName
})
function goToAgreement() {
  router.push('/agreement')
}

/**
 * 确认按钮
 */
function onClickRight() {
  isShowSelectCode.value = false
}

/**
 * 返回功能
 */
function onClickLeft() {
  isShowSelectCode.value = false
}
/**
 * 清空用户名
 */
function clearName(){
  userName.value = ''
}
/**
 * 显示或隐藏密码
 */
function togglePasswordVisibility() {
  // 动态切换输入框类型
  fieldType.value = fieldType.value === 'password' ? 'text' : 'password'
}
/**
 * 对称加密算法
 * @param json
 * @param CryptoJS
 * @returns {number|string}
 * @constructor
 */
function SymmetricEncryption(json, CryptoJS) {
  //json.input   输入的明文
  //json.key     加密密钥
  //return value 正常时返回：加密后的密码，异常时返回-1
  if (!json || !json.input || !json.key) {
    console.warn("json, json.input and json.key are not allow null!");
    return -1;
  }
  // 由于 key 小于16位会有问题，需要额外处理 key
  if (json.key.length === 0) {
    console.warn("json.length should not be zero!");
    return -1;
  }
  while (json.key.length < 16) {
    json.key = json.key + json.key;
  }
  const KEY = json.key; //32位
  const IV = "*BIM19FF4KMY0R8*"; //16位
  const key = CryptoJS.enc.Utf8.parse(KEY);
  const iv = CryptoJS.enc.Utf8.parse(IV);

  let encrypted = "";

  const srcs = CryptoJS.enc.Utf8.parse(json.input);
  encrypted = CryptoJS.AES.encrypt(srcs, key, {
    iv: iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
  });
  return encrypted.ciphertext.toString();
}
const PublicKey = 'MK4ZJF10PRO19*#8'
/**
 * 登录
 * @returns {Promise<void>}
 */
async function login() {
  if (userName.value && password.value){
    const res = await api.Login({
      IfSingleLogin: '',
      UserName: userName.value,
      Password: SymmetricEncryption({input: password.value + PublicKey, key: PublicKey}, CryptoJS)
    })
    if (res.data.Ret === 1) {
      // 保存token
      setToken(res.data.Data.token)
      // setAlpha(res.data.Data.alpha.replaceAll('+','-'))
      remember(userName.value,password.value)
      // 获取用户信息
      await getUserInfo(res.data.Data.token)
    }else {
      showToast(res.data.Msg)
    }
  }else {
    showToast('输入的数据不合法！')
  }

}

/**
 * 保存账号信息
 * @param userName
 * @param password
 */
function remember(userName, password) {
  if (rememberChecked.value){
    const remember = {
      userName,
      password
    }
    setRemember(JSON.stringify(remember))
    setIsAutoLogin(true)
    try {
      if (navigator.userAgent.indexOf('Android') > -1){
        mjs.toRemember(userName,password)
      }else if (navigator.userAgent.indexOf('iPhone') > -1) {
        window.webkit.messageHandlers.toRemember.postMessage(userName,password)
      }
    }catch (e) {
      console.error('保存账号信息失败', e)
    }
  }else {
    removeRemember()
  }
}

/**
 * 获取用户信息并保存
 * @param token
 */
async function getUserInfo(token) {
  const res = await api.GetUser({
    token
  })
  if (res.data.Ret === 1){
    setUserInfo(JSON.stringify(res.data.Data))
    setOrganizeId('')
    setTabIndex('')
    // await router.push('/project')
    await router.push('/home')


  }
}

onMounted(() => {
  const route = useRoute()
  const mToken = route.params.Token
  if (mToken) {
    setToken(mToken)
    getUserInfo(mToken)
  }
  // 获取保存账号记录
  const rememberStr = getRemember()
  if (rememberStr){
    rememberChecked.value = true
    const remember = JSON.parse(rememberStr)
    userName.value = remember.userName
    password.value = remember.password
    if (getIsAutoLogin() === 'true') {
      login()
    }
  }


  // 禁用双指放大
  document.documentElement.addEventListener('touchstart', function (event) {
    if (event.touches.length > 1) {
      event.preventDefault();
    }
  }, {
    passive: false
  });

  // 禁用双击放大
  let lastTouchEnd = 0;
  document.documentElement.addEventListener('touchend', function (event) {
    let now = Date.now();
    if (now - lastTouchEnd <= 300) {
      event.preventDefault();
    }
    lastTouchEnd = now;
  }, {
    passive: false
  });

})
</script>
<style scoped lang="scss">
.login-container{
  background-color: #f5f7fb;
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
    .title{
      margin-top: 50px;
      margin-left: 30px;
      font-size: 26px;
      color: #283A4F;
}
  .cell-group{
    margin-top: 30px;
    .van-cell{
      height: 60px;
    }
    .van-field{
      display: flex;
      align-items: center;
      justify-content: center;
      height: 60px;
    }
    .van-image{
      width: 24px;
      height: 24px;
    }
  }
  .remember-wrapper{
    margin: 16px 16px 0 16px;
    .van-checkbox{
      ::v-deep .van-checkbox__label{
        font-size: 12px;
      }
    }
  }
  .btn-wrapper{
    margin: 50px 16px 0 16px;
  }
  .popup-wrapper{
    background-color: #F4F5F6;
    .own-code{
      position: relative;
      align-items: center;
      display: flex;
      flex-direction: column;
      margin: 20px;
      width: calc(100% - 40px);
      height: 146px;
      background: white;
      box-shadow: 0px 4px 4px 0px rgba(41,44,54,0.01);
      border-radius: 6px;
      .icon{
        margin-top: 26px;
        width: 36px;
        height: 36px;
      }
      .check{
        top: 50%;
        right: 20px;
        position: absolute;
        width: 24px;
        height: 24px;
      }
      span{
        margin-top: 26px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 16px;
        color: #283A4F;
        line-height: 18px;
        letter-spacing: 1px;
        text-align: left;
        font-style: normal;
      }
    }
    .line{
      display: flex;
      align-items: center;
      justify-content: center;
      span{
        margin-left: 15px;
        margin-right: 15px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 13px;
        color: #283A4F;
        line-height: 16px;
        letter-spacing: 1px;
        text-align: left;
        font-style: normal;
      }
    }
    .customer-code{
      position: relative;
      align-items: center;
      display: flex;
      flex-direction: column;
      margin: 20px;
      width: calc(100% - 40px);
      height: 146px;
      background: white;
      box-shadow: 0px 4px 4px 0px rgba(41,44,54,0.01);
      border-radius: 6px;
      .icon{
        margin-top: 26px;
        width: 36px;
        height: 36px;
      }
      .van-field{
        margin-top: 20px;
        width: 160px;
      }
      span{
        margin-top: 26px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 16px;
        color: #283A4F;
        line-height: 18px;
        letter-spacing: 1px;
        text-align: left;
        font-style: normal;
      }
      .check{
        top: 50%;
        right: 20px;
        position: absolute;
        width: 24px;
        height: 24px;
      }
    }

  }
  .agreement-wrapper{
    width: 100%;
    position: absolute;
    bottom: 30px;
    display: flex;
    flex-direction: column;
    .top{
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      color: #283A4F;
      span{
        margin-right: 5px;
        color: #283A4F;
      }
      span:last-child{
        color: #007AFF;
        cursor: pointer;
      }

    }
    .ip{
      margin-top: 10px;
      font-size: 12px;
      color: #283A4F;
      text-align: center;
    }
  }

}
</style>
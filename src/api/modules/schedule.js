import {request} from "@/utils/request";

export default {
    GetTree: params => request({url: '/api/v1/plus/GetTree', params,baseURL:'TASK_URL',isToken:false}),
    GetProject: params => request({url: '/api/Schedual/Schedual/GetProjectByApp', params, isToken:false}),
    GetDetails: params => request({url: '/api/Schedual/Schedual/GetSingleProgressNewMobile', params, isToken:false}),
    GetScheduleProject: params => request({url: '/api/Schedual/Schedual/GetProject', params, isToken:false}),
    DeleteProject: (Token , Id ) => request({url: `/api/Schedual/Schedual/DeleteProject?Progress_ID=${Id}&token=${Token}`,method: 'post', isToken:false}),
    GetUnitTextConfig: params => request({url: '/api/Schedual/Schedual/GetUnitTextConfig', params,isToken:false}),
    Addproject : params => request({url: '/api/Schedual/Schedual/AddProgressNewMobile', method: 'post', params,headers: { 'Content-Type': 'multipart/form-data' },isToken:false}),
    SetUnitText : params => request({url: '/api/Schedual/Schedual/SetUnitText', method: 'post', params,isToken:false}),
    GetUserMenuTree :params => request({url: '/api/User/User/GetUserMenuTree', params,isToken:false}),
    EditProject: params => request({url: '/api/Schedual/Schedual/EditProgressNewMobile', method: 'post', params,headers: { 'Content-Type': 'multipart/form-data' },isToken:false}),
    Audit : params => request({url: '/api/Schedual/Schedual/Audit', method: 'post', params,isToken:false}),
    RemoveAttachment : params => request({url: '/api/Schedual/Schedual/RemoveAttachment', method: 'post', params,isToken:false})


}
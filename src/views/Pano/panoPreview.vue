<template>
  <div class="add-container" ref="previewContainer">
    <van-sticky>
      <van-nav-bar
          title="全景图预览"
          left-text="返回"
          left-arrow
          @click-left="onClickLeft"
      />
    </van-sticky>
    <div class="preview-container">
      <iframe class="preview" :src="iframeUrl" frameborder="0"></iframe>
    </div>
  </div>
</template>

<script setup>
import router from "@/router";
import {onBeforeUpdate, onMounted, onUnmounted, ref} from "vue";
import {getToken} from "@/utils/token";
import {useRoute} from "vue-router";
const route = useRoute()
const iframeUrl = ref('')
const previewContainer = ref()
/**
 * 左侧点击回退上一级路由
 * 注：replace 方法不会增加路由信息
 */
function onClickLeft(){
  router.back()
}

/**
 * 横屏显示处理
 */
function enableLandscapeMode() {
  const detectOrient = function() {
    let width = document.documentElement.clientWidth,
        height = document.documentElement.clientHeight,
        wrapper = previewContainer.value,
        style = "";

    if (width >= height) {
      // 横屏状态
      style += "width:" + width + "px;";
      style += "height:" + height + "px;";
      style += "-webkit-transform: rotate(0); transform: rotate(0);";
      style += "-webkit-transform-origin: 0 0;";
      style += "transform-origin: 0 0;";
    } else {
      // 竖屏状态，强制横屏显示
      style += "width:" + height + "px;";
      style += "height:" + width + "px;";
      style += "-webkit-transform: rotate(90deg); transform: rotate(90deg);";
      style += "-webkit-transform-origin: " + width / 2 + "px " + width / 2 + "px;";
      style += "transform-origin: " + width / 2 + "px " + width / 2 + "px;";
    }

    if (wrapper) {
      wrapper.style.cssText = style;
    }
  };

  window.addEventListener('resize', detectOrient);
  window.addEventListener('orientationchange', detectOrient);
  detectOrient();

  return () => {
    window.removeEventListener('resize', detectOrient);
    window.removeEventListener('orientationchange', detectOrient);
  };
}
let cleanupLandscape = null;

onMounted(()=>{
  const PbGuid = route.query.PbGuid
  const ProjectId = route.query.ProjectId
  const SceneName = route.query.SceneName
  if (PbGuid && ProjectId) {
    iframeUrl.value = SceneName ? `${window.IP_CONFIG.FRONT_URL}/#/LinkShare/PanoShare/${PbGuid}/${ProjectId}/${SceneName}` : `${window.IP_CONFIG.FRONT_URL}/#/LinkShare/PanoShare/${PbGuid}/${ProjectId}`
  }

  // 启用横屏模式
  cleanupLandscape = enableLandscapeMode()
})

onUnmounted(() => {
  // 清理横屏模式的事件监听器
  if (cleanupLandscape) {
    cleanupLandscape()
  }
})

</script>
<style scoped lang="scss">
.add-container{
  height: 100%;
  background-color: #FFF;
  .preview-container{
    height: calc(100% - 46px);
    .preview{
      width: 100%;
      height: 100%;
    }
  }
}

</style>

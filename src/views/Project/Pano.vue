<template>
  <div class="list-container">
    <van-sticky>
      <van-nav-bar
        :title="getProjectName()"
        left-text="返回"
        left-arrow
        @click-left="onClickLeft"
      />
      <div class="top-container">
        <van-dropdown-menu class="type-menu">
          <van-dropdown-item v-model="typeValue" :options="typeOption" />
        </van-dropdown-menu>
        <form action="/">
          <van-search class="search" v-model="searchValue" placeholder="查找全景图" @search="getList" @cancel="cancelSearch" />
        </form>
      </div>
    </van-sticky>
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
      <van-list
        class="list"
        v-model:loading="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="onLoad">
        <div v-for="item in listData" :key="item.ExamineID" @click="gotoDetails(item)">
          <van-swipe-cell>
            <div class="item-wrapper">
              <div class="left-wrapper">
                <van-image
                  class="left-img"
                  :src="getImageUrl(item)"
                />
              </div>
              <div class="right-wrapper">
                <div class="name">{{item.PbName + getStatus(item)}}</div>
                <div class="time">{{'标签名称: ' + item.LabelName}}</div>
                <div class="time">{{'采集时间: ' + item.PbUpdatetime.split(' ')[0]}}</div>
              </div>
            </div>
            <template #right>
              <div class="item-right-wrapper">
                <img :src="require('../../assets/images/pano/pano-edit.png')" alt="" @click="editItem(item)">
                <img :src="require('../../assets/images/quality/delete-item.png')" alt="" @click="deleteItem(item.PbGuid)">
              </div>
            </template>
          </van-swipe-cell>
        </div>
      </van-list>
    </van-pull-refresh>
    <van-image
      class="add-img"
      width="60"
      height="60"
      :src="require('../../assets/images/common/add-btn.png')"
      @click="addPano"
    ></van-image>
  </div>

</template>
<script setup>
import router from "@/router";
import {onMounted, ref, watch} from "vue";
import api from "@/api";
import {getOrganizeId} from "@/utils/organizeId";
import {useRoute} from "vue-router";
import {showToast} from "vant";
import {getToken} from "@/utils/token";
import {getProjectName} from "@/utils/projectName";
const token = ref('')
const organizeId = ref('')
const typeValue = ref('');
let typeOption = [
  { text: '全部标签', value: '' }
];
const listData = ref([])
let loading = ref(false);
const finished = ref(false);
const refreshing = ref(false);
const searchValue = ref('');
/**
 * 刷新列表
 */
function onRefresh() {
  // 清空列表数据
  finished.value = false;
  // 重新加载数据
  // 将 loading 设置为 true，表示处于加载状态
  loading.value = true;
  // 加载数据
  getList();
  // 取消刷新状态
  refreshing.value = false
}
/**
 * 左侧点击回退上一级路由
 * 注：replace 方法不会增加路由信息
 */
function onClickLeft(){
  router.push('/home')
}
/***
 * 获取图片地址
 */
function getImageUrl(item) {
  return `${window.IP_CONFIG.BASE_URL}/Panorama${item.PbUrl}/cover.png`
}
/**
 * 列表加载方法
 */
function onLoad(){
}
/**
 * 取消搜索
 */
function cancelSearch(){
  searchValue.value=''
  getList()
}
/**
 * 获取质量数据
 */
async function getList() {
  loading.value = true
  const res = await api.GetListByLabelGroup({
    Token: token.value,
    organizeId: organizeId.value,
    labelId: typeValue.value,
    pbName: searchValue.value
  })
  if (res.data.Ret === 1){
    let arrM = []
    for (const obj of res.data.Data) {
      const arr = obj.Panoramas
      arrM.push(...arr)
    }
    listData.value = arrM
  }
  finished.value = true
  loading.value = false
}
/**
 * 获取检查类型
 * @returns {Promise<void>}
 */
async function getTypes(){
  const res = await api.GetLabelList(
      {
        Token: token.value,
        OrganizeId: organizeId.value
      }
  )
  if (res.data.Ret === 1) {
   const typeData = res.data.Data
    typeOption = [
      { text: '全部标签', value: '' }
    ]
    for (const label of typeData) {
      typeOption.push({
        text: label.LabelName,
        value: label.LabelId
      })
    }
  }
}
/**
 * 跳转路由到新建页面
 */
function addPano() {
  router.push({
    path:'/addPano'
  })
}
/**
 * 路由跳转预览页面
 */
function gotoDetails(item) {
  router.push({
    path:'/panoPreview',
    query: {
      PbGuid: item.PbGuid,
      ProjectId: organizeId.value
    }
  })
}
/**
 * 删除单条数据
 * @param id
 * @returns {Promise<void>}
 */
async function deleteItem(PbGuid) {
  const res = await api.RemovePanoItem(token.value, {
    PbGuid: PbGuid
  })
  if (res.data.Ret === 1){
    showToast(res.data.Msg)
    await getList()
  }else {
    showToast(res.data.Msg)
  }
}

/**
 * 编辑数据进入子集
 * @param id
 * @returns {Promise<void>}
 */
async function editItem(item) {
  router.push({
    path:'/panoSubset',
    query: {
      PbGuid: item.PbGuid,
      PbUrl: item.PbUrl,
      Name: item.PbName
    }
  })
}
/**
 * 获取全景图转换状态
 */
function getStatus(item) {
  let status = ''
  if (item.PqFlag == 0) {
    status = '(待转换)'
  }else if (item.PqFlag == 1){
    status = '(转换中)'
  }else if (item.PqFlag == 3){
    status = '(转换失败)'
  }
  return status
}
onMounted(()=>{
  token.value = getToken()
  organizeId.value = getOrganizeId()
  getList()
  getTypes()
})
watch(typeValue, async (newValue,oldValue) =>{
  await getList()
})
</script>

<style scoped lang="scss">
.list-container{
  height: 100%;
  background-color: #f2f2f2;
  ::-webkit-scrollbar{
    display: none;
  }
  .top-container{
    display: flex;
    .type-menu{
      width: 50%;
      :deep .van-dropdown-menu__bar{
        box-shadow: none;
      }
    }
    .search{
      height: 100%;
    }
  }
  .list{
    padding: 16px;
    overflow-y: scroll;
    margin-bottom: 48px;
    .item-wrapper{
      display: flex;
      margin-bottom: 16px;
      padding: 12px;
      border-radius: 4px;
      background-color: white;
      .left-wrapper{
        width: 118px;
        height: 88px;
        border-radius: 4px;
        .left-img{
          width: 100%;
          height: 100%;
        }
      }
      .right-wrapper{
        margin-left: 8px;
        width: calc(100% - 126px);
        .name{
          width: 100%;
          height: 22px;
          font-size: 16px;
          font-weight: 500;
          color: #323233;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .time{
          width: 100%;
          height: 22px;
          margin-top: 10px;
          font-size: 16px;
          font-weight: 400;
          color: #7a7a7d;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
    .item-right-wrapper{
      height: 100%;
      img{
        width: auto;
        height: auto;
        max-width: 100%;
        max-height: 100%;

      }
    }
  }
  .add-img{
    position: fixed;
    bottom: 100px;
    right: 16px;
  }
  ::v-deep .van-search{
    padding: 0;
    .van-search__content{
      background: none;
    }
  }
}

</style>
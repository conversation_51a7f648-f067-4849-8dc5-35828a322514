/**
 * 全景图
 */
import { request } from '@/utils/request'
export default {
  GetListByLabelGroup:params => request({url:'/api/Panorama/PanoramaFile/GetListByLabelGroup', params, isToken:false}),
  GetLabelList:params => request({url:'/api/Panorama/Label/GetList', params, isToken:false}),
  RemovePanoItem:(token, params) => request({url:`/api/Panorama/PanoramaFile/RemoveItem?Token=${token}`, method: 'post', params, isToken:false}),
  GetPanoramaSceneList:params => request({url:'/api/Panorama/PanoramaScene/GetPanoramaSceneList', params, isToken:false}),
  RemoveScene:params => request({url:'/api/Panorama/PanoramaFile/RemoveScene', method: 'post', params, isToken:false}),
  UploadPanoImages:(token, params) => request({url:`/api/Panorama/PanoramaFile/UploadImages?Token=${token}`, method: 'post', params, headers: { 'Content-Type': 'multipart/form-data' }, isToken:false}),
}
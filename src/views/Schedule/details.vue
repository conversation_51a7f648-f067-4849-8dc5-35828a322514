<template>
  <div class="add-container">
    <van-sticky>
      <van-nav-bar
          :title="tittle"
          left-text="返回"
          left-arrow
          @click-left="onClickLeft"
      />

    </van-sticky>

    <!-- 填报日期 start-->
    <van-popup v-model:show="isShowCreatePicker" position="bottom">
      <van-date-picker @confirm="onConfirmCreateTime" @cancel="isShowCreatePicker = false" v-model="currentDate" title="请选择填报日期"/>
    </van-popup>
    <!-- 填报日期 end-->
    <!-- 实际结束时间选择 start-->
    <van-popup v-model:show="isShowEndPicker" position="bottom">
      <van-date-picker @confirm="onConfirmEndTime" @cancel="isShowEndPicker = false" v-model="currentDate" title="请选择实际结束日期"/>
    </van-popup>
    <!-- 实际结束时间选择 end-->
    <!--填报信息 start-->
    <div class="form-container mt-10">
      <van-cell-group inset class="mt-10 pt-12">
        <span class="font-weight-500 mt-12 pt-12 ml-10">基本信息</span>
        <div class="form-item">
          <span>任务名称</span>
          <div class="line"></div>
          <div class="bg-right" @click="getAllTree">
            <span>{{item.Progress_Name}}</span>
          </div>
        </div>
        <div class="form-item">
          <span>计划名称</span>
          <div class="line"></div>
          <div class="bg-right">
            <span>{{item.Progress_PlanName}}</span>
          </div>
        </div>
        <div class="form-item">
          <span>计划日期</span>
          <div class="line"></div>
          <div class="bg-right">
            <span>{{taskPlanTime}}</span>
          </div>
        </div>
        <div class="form-item">
          <span>呈现方式</span>
          <div class="line"></div>
          <div class="bg-right">
            <van-popover v-model:show="isShowPopover" :actions="actions" trigger='manual' >
              <template #reference>
                <span>{{type}}</span>
              </template>
            </van-popover>
            <van-icon class="right-drop" name="arrow-down" v-if="false"/>
          </div>
        </div>
        <div class="form-item" v-if="!isShowExtra && !isProgress">
          <span>{{allTips}}</span>
          <div class="line"></div>
          <div class="bg-right">
            <input v-model="allProgress"  placeholder="请输入" type="number" :disabled="disabledPopover"/>
          </div>
        </div>

      </van-cell-group>
      <van-cell-group inset class="mt-10 pt-12 pb-12">
        <span class="font-weight-500 mt-12 pt-12 ml-10">计划任务完成情况</span>
        <div class="form-item">
          <span>任务状态</span>
          <div class="line"></div>
          <div class="bg-right">
            <span>{{state}}</span>
          </div>
        </div>
        <div class="form-item-item mt-12 pt-12 ml-10 mr-10" v-if="isShowExtra">
          <span class="ml-10">任务高程</span>
          <div class="item">
            <span class="ml-10">底部：</span>
            <div class="bg-right">
              <input v-model="lowTotal"  placeholder="请输入底部高程" :disabled="disabledPopover"/>
            </div>
          </div>
          <div class="line-2"></div>
          <div class="item">
            <span class="ml-10">顶部：</span>
            <div class="bg-right">
              <input v-model="highTotal"  placeholder="请输入顶部高程" :disabled="disabledPopover"/>
            </div>
          </div>
        </div>

        <div class="form-item">
          <span>填报日期</span>
          <div class="line"></div>
          <div class="bg-right" @click="openTime">
            <span style="color: #424d5f">{{createTime}}</span>
            <van-image
                v-if="auditType === 0 || auditType === 3"
                class="date-select"
                width="20"
                height="20"
                :src="require('../../assets/images/schedule/date-select.png')"
            ></van-image>
          </div>

        </div>
        <div class="form-item-item mt-12 pt-12 ml-10 mr-10">
          <span class="ml-10">实际日期</span>
          <div class="item">
            <span class="ml-10">开始：</span>
            <div class="bg-right">
              <span>{{splitTime(item.Progress_actualstarttime)}}</span>
            </div>
          </div>
          <div class="line-2"></div>
          <div class="item">
            <span class="ml-10">结束：</span>
            <div class="bg-right">
              <span>{{splitTime(item.Progress_actualendtime)}}</span>
            </div>
            <van-image
                v-if="!disabledEndTimePopover"
                @click="openEndTimePicker()"
                class="date-select"
                width="20"
                height="20"
                :src="require('../../assets/images/schedule/date-select.png')"
            ></van-image>
          </div>
        </div>
        <div class="form-item-item mt-12 pt-12 ml-10 mr-10">
          <span class="ml-10">完成比例</span>
          <div class="item">
            <span class="ml-10">计划：</span>
            <div class="bg-right">
              <span>{{m_adding_planvalue}}</span>
              <span class="right-unit">{{unitType}}</span>
            </div>
          </div>
          <div class="line-2"></div>
          <div class="item">
            <span class="ml-10">实际：</span>
            <div class="bg-right">
              <input v-model="actProgress"  placeholder="请输入"  @change="evt_percentchange" type="number" :disabled="auditType === 1 || item.Progress_actualvalue === 100"/>
              <span>{{unitType}}</span>
            </div>
          </div>
        </div>
      </van-cell-group>
      <van-cell-group inset class="mt-10 pt-12 pb-12">
        <span class="font-weight-500 ml-10 upload-container">进度形象照片</span>
        <van-uploader
            class="ml-10 mt-10"
            v-model="fileList"
            max-count="9"
            :deletable="isCanEditPhoto"
            :disabled="!isCanEditPhoto"
            :before-delete="removePhoto"
        />
      </van-cell-group>

      <!--保存 直接提交-->
      <div class="func-wrapper" v-if="isShowFabuBtn">
        <div class="no" @click="onClickRight()">
          <span>保存</span>
        </div>
        <div :class="auditType!==3?'yes':'yes-no'"  @click="audit(0)">
          <span>提交</span>
        </div>
      </div>
      
      <!--审核 驳回-->
      <div class="func-wrapper" v-if="isShowAuditBtn">
        <div class="no" @click="audit(2)">
          <span>驳回</span>
        </div>
        <div class="yes"  @click="audit(1)">
          <span>审批</span>
        </div>
      </div>
    </div>
    <!--填报信息 end-->

  </div>
</template>


<script setup>
import router from "@/router";
import {computed, onMounted, ref} from "vue";
import api from "@/api";
import {getToken} from "@/utils/token";
import {useRoute} from "vue-router";
import {getCurrentDate, getCurrentTime} from "@/utils";
import {splitTime} from "@/utils/timeSplit";
import {getUserInfo} from "@/utils/user";
import {showToast} from "vant";
import {getOrganizeId} from "@/utils/organizeId";
import {getAu} from "@/utils/au";

const allProgress = ref(0)
const createTime = ref('')
const isShowCreatePicker = ref(false)
const planProgress= ref(0)
const actProgress= ref(0)
const state = ref('状态')
const lowTotal = ref(0)
const highTotal = ref(0)
const isProgress = ref(true)


function openTime(){
  if (auditType.value === 0 || auditType.value === 3){
    isShowCreatePicker.value = true
  }
}

const disabledEndTimePopover = ref(true)
const isShowEndPicker = ref(false)
/**
 * 打开实际时间选择
 */
function openEndTimePicker(){
  if (!disabledEndTimePopover.value){
    isShowEndPicker.value = true
  }
}

/**
 * 结束日期确认
 * @param selectedValues
 */
const onConfirmEndTime = ({ selectedValues }) => {
  item.value.Progress_actualendtime = selectedValues.join('-');
  isShowEndPicker.value = false;
}
/**
 * 发布，审核，驳回操作
 * @param type
 */
async function audit(type) {
  if (auditType.value === 3){
    return
  }
  const params = {
    Token: getToken(),
    AuditType: type,
    ProgressIds: [item.value.Progress_ID],
  }
  let auditDescription =''
  if (type === 1){
    auditDescription = '同意'
    params.AuditDescription = auditDescription
  }else if (type === 2){
    auditDescription = '请校核后再提交'
    params.AuditDescription = auditDescription
  }
  const res = await api.Audit(params)
  if (res.data.Ret === 1) {
      switch (type) {
        case 0:
          showToast('提交成功!')
          break
        case 1:
          showToast('审批成功!')
          break
        case 2:
          showToast('驳回成功!')
          break
      }
      router.back()
  }else {
    showToast(res.data.Msg)
  }
}
/**
 * 填报日期确认
 * @param selectedValues
 */
const onConfirmCreateTime = ({ selectedValues }) => {
  createTime.value = selectedValues.join('-');
  isShowCreatePicker.value = false;
  computeTimeDiff()
}

/**
 * 左侧点击回退上一级路由
 * 注：replace 方法不会增加路由信息
 */
function onClickLeft(){
  router.back()
}
function onClickRight(){
  edit()
}

const taskPlanTime = computed(() => {
  if (item.value){
    let s = splitTime(item.value.Progress_planstarttime)
    let e = splitTime(item.value.Progress_plannendtime)
    return s + ' 至 ' + e
  }
  return ''
})

const unitType = ref('')
const isShowPopover = ref(false);
const actions = [
  { text: '百分比%' },
  { text: '里程m' },
  { text: '里程km' },
  { text: '高程m' }
];
const isShowExtra = ref(false)
const type = ref('请选择')
const allTips = ref('任务总量')


const unit = ref({
  UnitType: 1
})
const disabledPopover = ref(true)

/**
 * 获取单位
 * @returns {Promise<void>}
 */
async function getUnitTextConfig() {
  const res = await api.GetUnitTextConfig({
    Token: getToken(),
    uid: item.value.Progress_treeID,
  })
  if (res.data.Ret === 1) {
    unit.value = res.data.Data
    // 不为null时已经填报过
    if (unit.value.UnitType){
        switch (unit.value.UnitType) {
          case 1:
            isProgress.value = true
            allTips.value = '任务总量'
            type.value = '百分比%'
            unitType.value = '%'
            break
          case 2:
            isProgress.value = false
            allTips.value = '任务里程'
            type.value = '里程m'
            unitType.value = 'm'
            break
          case 3:
            isProgress.value = false
            allTips.value = '任务里程'
            type.value = '里程km'
            unitType.value = 'km'
            break
          case 4:
            isProgress.value = false
            isShowExtra.value = true
            type.value = '高程m'
            unitType.value = 'm'
            lowTotal.value = unit.value.UnitValue.split(',')[0]
            highTotal.value = unit.value.UnitValue.split(',')[1]
            break
        }
    }else {
      isShowExtra.value = true
      unit.value.UnitType = 1
      planProgress.value = 100
      m_adding_planvalue.value =100
      allProgress.value = 100
      type.value = '百分比%'
      isProgress.value = false
    }
  }
}

/**
 * 获取两个日期对象差值
 * @param {*} sDate1 日期 date对象 或者 字符串
 * @param {*} sDate2   日期 date对象 或者 字符串
 * @returns
 */
function AbsDateDiff(sDate1, sDate2) {
  let aDate, oDate1, oDate2, iDays;
  oDate1 = new Date(sDate1).getTime();
  oDate2 = new Date(sDate2).getTime();
  iDays = parseInt((oDate1 - oDate2) / 1000 / 60 / 60 / 24) //把相差的毫秒数转换为天数
  return  iDays;
}

/**
 * 获取两个日期对象差值
 * @param {*} sDate1 日期 date对象 或者 字符串
 * @param {*} sDate2   日期 date对象 或者 字符串
 * @returns
 */
function DateDiff(sDate1, sDate2) {
  let aDate, oDate1, oDate2, iDays
  oDate1 = new Date(sDate1).getTime()
  oDate2 = new Date(sDate2).getTime()
  iDays = parseInt((oDate1 - oDate2) / 1000 / 60 / 60 / 24) //把相差的毫秒数转换为天数
  return Math.abs(iDays)
}

/**
 * 判断当前日期是否在计划日期范围之内
 * @param _adding
 * @param beginDateStr
 * @param endDateStr
 * @returns {boolean}
 */
function isDuringDate (_adding,beginDateStr, endDateStr) {
  let curDate = new Date(_adding),
      beginDate = new Date(beginDateStr),
      endDate = new Date(endDateStr);
  if (curDate >= beginDate && curDate <= endDate) {
    return true;
  }
  return false;
}
const TimeDiffAdd = ref('')
const m_planpercentStr = ref('')
const m_planpercent = ref(0)
const m_adding_planvalue = ref(0)
const m_addingpercent = ref(0)
const m_adding_actualvalue = ref(0)

/**
 * 进度填报的实际完成进度
 *
 */
function evt_percentchange() {
  m_adding_actualvalue.value = actProgress.value;
  // 超前滞后根据计划完成进度和实际完成进度计算
  if(m_adding_planvalue.value * 1 < m_adding_actualvalue.value * 1){
    state.value = '超前';
  }
  if(m_adding_planvalue.value * 1 > m_adding_actualvalue.value * 1){
    state.value = '滞后';
  }
  if(m_adding_planvalue.value * 1 == m_adding_actualvalue.value * 1){
    state.value = '正常';
  }
  // 百分比：实际完成比例 = 实际完成进度
  // 里程： 实际完成比例 = （实际完成进度/总进度）*100
  // 高程：实际完成比例 = （实际完成进度/顶部高层）*100
  switch (unit.value.UnitType) {
    case 1:
      m_addingpercent.value = m_adding_actualvalue.value;
      break;
    case 2:
    case 3:
      m_addingpercent.value = ((m_adding_actualvalue.value * 1) / (allProgress.value * 1) ) * 100
      break;
    case 4:
      m_addingpercent.value = (m_adding_actualvalue.value / (highTotal.value * 1 - lowTotal.value * 1) ) * 100
      break;
  }

}
/**
 * 计算时间差值
 */
function computeTimeDiff(){
  let startPlanTime = item.value.Progress_planstarttime.substr(0,10);
  let endPlanTime = item.value.Progress_plannendtime.substr(0,10);

  let TimeDiff = DateDiff(startPlanTime,endPlanTime) + 1; // 计划天数
  TimeDiffAdd.value = TimeDiff;

  let TimeDiffPer = DateDiff(startPlanTime,createTime.value);  // 计划开始时间和填报时间的时间差

  let diffdate = isDuringDate(createTime.value,startPlanTime,endPlanTime);  // 查看填报时间是否在开始时间和结束时间内
  let diffPer = (100 / TimeDiff); // 100/计划天数，计算每天的百分比
  let tdiffper = TimeDiffPer * diffPer; // 天数差 * 每天的百分比 = 当前的计划百分比

  if(diffdate){
    let diffLastPer = (diffPer*1 + tdiffper*1 ).toFixed(1);
    if(diffLastPer > 100){
      diffLastPer = 100
    }else{
      diffLastPer = (diffPer*1 + tdiffper*1 ).toFixed(1);
    }
    // 计算计划完成比例
    m_planpercentStr.value = diffLastPer + '%';
    m_planpercent.value = diffLastPer * 1;
    planProgress.value = diffLastPer * 1
    // 在当前开始-结束时间范围内=计算计划完成进度
    // 百分比：计划完成进度 = 计划完成比例 _this.m_planpercent
    // 里程：计划完成进度 = 总里程数*（计划完成比例/100）  方便计算，直接拿计划完成比例计算了
    // 高程：计划完成进度 = （（（顶部高程-底部高程）/ 总计划天数）* 当前第几天）+ 底部高程
    // （（顶部高层-底部高层）/（（当前天数进度高层+底部高层））*100%
    state.value = toCalculatePercentState(m_planpercent.value,actProgress.value)
    console.log('unit',unit.value.UintType)
    switch (unit.value.UnitType) {
      case 1:
        m_adding_planvalue.value = m_planpercent.value
        planProgress.value = m_adding_planvalue.value
        break;
      case 2:
      case 3:
        m_adding_planvalue.value = (allProgress.value * (m_planpercent.value / 100)).toFixed(2);
        planProgress.value = m_adding_planvalue.value
        break;
      case 4:
        let _val = ((Number(highTotal.value) - Number(lowTotal.value)) / TimeDiff) * TimeDiffPer * 1;
        m_adding_planvalue.value = (_val * 1 + lowTotal.value * 1).toFixed(2)
        planProgress.value = m_adding_planvalue.value
        break;
    }
  }else{
    if(AbsDateDiff(createTime.value,startPlanTime) < 1){
      m_planpercentStr.value = '0%';
      m_planpercent.value = 0;
      state.value = '超前';
      switch (unit.value.UnitType) {
        case 1:
          m_adding_planvalue.value = 0;
          break;
        case 2:
        case 3:
          m_adding_planvalue.value = 0;
          break;
        case 4:
          m_adding_planvalue.value  = unit.value.UnitValue * 1
          break;
      }
    }else{
      m_planpercentStr.value = '100%';
      m_planpercent.value = 100
      state.value = '滞后';
      switch (unit.value.UnitType) {
        case 1:
          m_adding_planvalue.value = 100;
          break;
        case 2:
        case 3:
          m_adding_planvalue.value = allProgress.value
          break;
        case 4:
          m_adding_planvalue.value = highTotal.value * 1
          break;
      }
    }
  }
}

/**
 * 计算状态
 * @param planPercent
 * @param actualPercent
 * @returns {string}
 */
function toCalculatePercentState(planPercent,actualPercent){
  let setToleranceValues = 0;
  // 目前没有容差值，setToleranceValues值先为0
  // 当实际完成时间在计划时间范围内使用该方法
  // 当实际比例在planPercent ± setToleranceValues内，属于正常,小于属于滞后，大于属于超前
  let percentState = '';
  if(actualPercent >= planPercent - setToleranceValues  && actualPercent <= planPercent+setToleranceValues){
    percentState = '正常';
  }else if(actualPercent > planPercent - setToleranceValues){
    percentState = '超前';
  }else{
    percentState = '滞后';
  }
  return percentState;
}

const user = JSON.parse(getUserInfo())

/**
 * 编辑进度填报
 * @returns {Promise<void>}
 */
async function edit() {
  const form = new FormData
  form.append('token',getToken());
  form.append('Progress_PlanName',item.value.Progress_PlanName)
  form.append('Progress_Name',item.value.Progress_Name)
  form.append('Progress_state',state.value);
  form.append('Progress_createuser',user.RealName);
  form.append('Progress_createuserid',user.UserId);
  form.append('Progress_planstarttime',item.value.Progress_planstarttime);
  form.append('Progress_plannendtime',item.value.Progress_plannendtime);
  form.append('Progress_actualstarttime',item.value.Progress_actualstarttime);
  form.append('Progress_planfate',TimeDiffAdd.value);
  form.append('Progress_ID',item.value.Progress_ID);
  form.append('Progress_treeID',item.value.Progress_treeID);
  form.append('Progress_ProjectID',item.value.Progress_ProjectID);
  form.append('Progress_parentid',item.value.Progress_parentid);
  form.append('Progress_unittime',createTime.value);
  form.append('IsSubmitAudit',false);
  form.append('Progress_planvalue',m_adding_planvalue.value);
  if (item.value.Progress_actualendtime){
    form.append('Progress_actualendtime',item.value.Progress_actualendtime);
    form.append('Progress_actualratio',100)
    form.append('Progress_planratio',100)
    form.append('Progress_actualvalue',Number.parseFloat(allProgress.value))
  }else {
    form.append('Progress_planratio',m_planpercent.value)
    form.append('Progress_actualratio',m_addingpercent.value)
    form.append('Progress_actualvalue',actProgress.value)
  }
  if (Number(m_addingpercent.value) === 100){
    if (!item.value.Progress_actualendtime){
      showToast('请填写实际结束日期')
      return
    }
  }

  if (fileList.value.length > 0){
    const temp = fileList.value.filter((xx)=> xx.file)
    if (temp.length > 0){
      for (const item of temp) {
        form.append('Files',item.file)
      }
    }
  }
  const res = await api.EditProject(form)
  if (res.data.Ret === 1){
    showToast('修改成功')
    router.back()
  }else {
    showToast(res.data.Msg)
  }
}

const item = ref('')
const tittle = ref('')
const route = useRoute()
const currentDate = ref([])
const isCanAdd = ref(false)
const auditType = ref(0)
const isShowAuditBtn  = ref(false)
const isShowFabuBtn  = ref(false)
const fileList = ref([])
const isCanEditPhoto = ref(false)
onMounted(()=>{
  const au = JSON.parse(getAu())
  isCanAdd.value = au === true;
  currentDate.value = getCurrentDate()
  createTime.value = getCurrentTime().split(' ')[0]
  if (route.query.id) {
    getDetails(route.query.id)
  }
})

/**
 * 处理数组图片
 * @param val
 */
function handelImgList(val) {
  if (val.length > 0){
    for (const valElement of val) {
      const temp = window.IP_CONFIG.BASE_URL + '/' + valElement.AttachmentUrl
      fileList.value.push(
          {
            url:temp,
            id: valElement.AttachmentID
          }
      )
    }
  }
}

/**
 *  移除图片
 * @param file
 * @param detail
 */
async function removePhoto(file,detail) {
  if (file.id){
    const params = {
      Token: getToken(),
      AttachmentID: file.id
    }
    const res = await api.RemoveAttachment(params)
    if (res.data.Ret === 1) {
      fileList.value.splice(detail.index , 1)
    } else {
      showToast(res.data.Msg)
    }
  }else {
    fileList.value.splice(detail.index , 1)
  }
}

/**
 * 获取详情
 * @param progressID
 * @constructor
 */
async function getDetails(progressID) {
  const result = await api.GetDetails({
    progressID,
    Token: getToken()
  })
  if (result.data.Ret === 1) {
    item.value = result.data.Data
    await getUnitTextConfig()
    handelImgList(item.value.ListAttachment)
    tittle.value = '进度详情'
    createTime.value = splitTime(item.value.Progress_unittime)
    planProgress.value = item.value.Progress_planvalue
    actProgress.value = item.value.Progress_actualvalue
    state.value = item.value.Progress_state
    auditType.value = item.value.AuditStatus
    // allProgress.value = item.value.Progress_planvalue
    allProgress.value = unit.value.UnitValue
    m_adding_planvalue.value = item.value.Progress_planvalue
    m_addingpercent.value = item.value.Progress_actualratio
    m_planpercent.value = item.value.Progress_planratio
    TimeDiffAdd.value = item.value.Progress_planfate
    if (!isCanAdd.value){
      isCanEditPhoto.value = false
      // 监理人审核
      if (auditType.value === 1){
        // 待审核状态
        isShowAuditBtn.value = true
      }
    }else {
      // 编辑，发布
      if (auditType.value === 0 || auditType.value ===3){
        isShowFabuBtn.value = true
        isCanEditPhoto.value = true
        if (item.value.Progress_actualendtime){
          disabledEndTimePopover.value = true
        }else {
          disabledEndTimePopover.value = false
        }
      }
    }
  }else {
    showToast(result.data.Msg)
    router.back()
  }
}
</script>
<style scoped lang="scss">
.add-container{
  height: 100%;
  background-color: #F4F5F6;
  .header-container{
    display: flex;
    position: relative;
    align-items: center;
    justify-content: center;
    padding-top: 10px;
    padding-bottom: 10px;
    width: 100%;
    background-color: white;
    img{
      position: absolute;
      left: 0;
    }
    span:first-of-type{
      color: black;
    }
    span:last-child{
      position: absolute;
      right: 0;
      margin-right: 10px;
      font-weight: 500;
      color: #007AFF;
    }
  }
  .hide-overflow{
    overflow: hidden;
  }
  .form-container{
    padding-bottom: 40px;
    background-color: #F4F5F6;
    .form-item-item{
      border-radius: 4px;
      border: 0.5px solid #D8D8D8;
      .line-2{
        margin-left: 10px;
        margin-right: 10px;
        width: calc(100% - 20px);
        height: 1px;
        background: #D8D8D8;
      }
      .item{
        display: flex;
        align-items:center;
        height: 40px;
        span{
          font-size: 13px;
        }

        .bg-right{
          position: relative;
          height: 100%;
          span{
            display: inline-block;
            width: 80px;
            line-height: 40px;
            color: #a9a9a9;
            font-size: 13px;
          }
          input{
            width: 80px;
            line-height: 40px;
            background-color: transparent;
            outline-style: none ;
            border: none;
            font-size: 13px;
          }
        }
        .date-select{
          margin-left: auto;
          margin-right: 12px;
        }
      }

    }

    .form-item{
      margin: 10px 10px 10px 10px;
      display: flex;
      align-items:center;
      height: 40px;
      border-radius: 4px;
      border: 0.5px solid #D8D8D8;
      span{
        display: inline-block;
        width: 100px;
        margin-left: 10px;
        font-size: 14px;
      }
      .line{
        margin-left: 10px;
        width: 1px;
        height: 20px;
        background: #D8D8D8;
      }
      .bg-right{
        position: relative;
        height: 100%;
        width: 100%;
        span{
          width: 210px;
          /* 超出部分隐藏 */
          overflow: hidden;
          /* 不换行 */
          white-space: nowrap;
          /* 溢出用省略号代替 */
          text-overflow: ellipsis;
          line-height: 40px;
          color: #a9a9a9;
          margin-left: 20px;
          font-size: 15px;
        }
        input{
          color: #a9a9a9;
          line-height: 40px;
          margin-left: 20px;
          background-color: transparent;
          outline-style: none ;
          border: none;
          font-size: 15px;
        }
        .right-unit{
          margin-right: 6px;
          text-align: right;
        }
        .right-drop{
          margin-left: 6px;
        }
        .date-select{
          position: absolute;
          right: 0;
          top: 50%;
          margin-top: -10px;
          margin-right: 12px;
        }
      }

      .bg-img-right{
        display: flex;
        align-items: center;
        margin-right: 16px;
        width: 70%;
        height: 100%;
        border-radius: 6px;
        border: 1px solid #6C6C6C;
        img{
          margin-left: 12px;
        }
        span{
          width: 100%;
          line-height: 40px;
          color: #a9a9a9;
          font-size: 15px;
        }
      }
    }
    .upload-container{
      display: flex;
      flex-direction: column;
    }
  }
  .btn-fabu{
    position: absolute;
    right: 0;
    bottom: 30px;
  }
  .func-wrapper{
    height: 50px;
    margin:12px;
    display: flex;
    span{
      font-size: 15px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
    }
    .no{
      margin-right: 2px;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      width: 50%;
      color: #007AFF;
      background-color: white;
      border-radius: 4px;
      border: 1px solid #007AFF;
    }
    .yes{
      margin-left: 2px;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      width: 50%;
      color: #ffffff;
      border-radius: 4px;
      background-color: #007AFF;
    }
    .yes-no{
      margin-left: 2px;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      width: 50%;
      color: #ffffff;
      border-radius: 4px;
      background-color: #99caff;
    }
  }
}

</style>
<template>
  <div class="modify-container">
    <van-sticky>
      <van-nav-bar
          title="修改密码"
          left-text="返回"
          left-arrow
          @click-left="onClickLeft"
      />
    </van-sticky>
    <van-form>
      <van-cell-group inset class="cell-group">
        <van-field
            autocomplete="new-password"
            v-model="newPassword"
            name="新密码"
            label="新密码"
            placeholder="请输入新密码"
            :rules="[{ required: true, message: '请输入新密码' }]"
        />
      </van-cell-group>

      <van-cell-group inset class="cell-group">
        <van-field
            autocomplete="new-password"
            v-model="repeatPassword"
            type="password"
            name="重复密码"
            label="重复密码"
            placeholder="请输入重复密码"
            :rules="[{ required: true, message: '请输入重复密码' }]"/>
      </van-cell-group>

      <div class="btn-wrapper">
        <van-button round block type="primary" @click="modifyPassword">
          确认
        </van-button>
      </div>
    </van-form>
  </div>
</template>
<script setup>
import {ref} from "vue";
import api from "@/api";
import {getToken} from "@/utils/token";
import {showToast} from "vant";

const repeatPassword = ref('')
const newPassword = ref('')
let emit = defineEmits(['close'])

/**
 * 点击返回上一级路由
 */
function onClickLeft() {
  emit('close')
}

/**
 * 修改密码
 */
async function modifyPassword() {
  if (newPassword.value === repeatPassword.value) {
    const result = await api.ModifyUserInfo({
      Pwd: newPassword.value,
      PwdRepeat: repeatPassword.value,
      Token: getToken(),
      Type: 4
    })
    if (result.data.Ret === 1) {
      showToast('修改密码成功!')
      onClickLeft()
    }else {
      showToast(result.data.Msg)
    }
  }else {
    showToast('两次输入的密码不一致！')
  }
}
</script>

<style scoped lang="scss">
.modify-container{
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 10;
  background-color: #f5f7fb;
  .cell-group{
    margin-top: 20px;
  }
  .btn-wrapper{
    margin: 80px 16px 0 16px;
  }

}
</style>
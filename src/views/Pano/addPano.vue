<template>
  <div class="add-container">
    <van-sticky>
      <van-nav-bar
          title="添加全景图"
          left-text="返回"
          left-arrow
          @click-left="onClickLeft"
          right-text="保存"
          @click-right="onClickSave"
      />
    </van-sticky>
    <div class="from-container">
      <van-cell-group inset class="mt-10 pt-12">
        <van-field
          v-if="!PbGuid"
          v-model="panoname"
          name="图集名称"
          label="图集名称"
          placeholder="请输入全景图集名称"
          :rules="[{ required: true, message: '请输入全景图集名称' }]"
        />
        <van-field
          v-if="!PbGuid"
          v-model="result.text"
          is-link
          readonly
          name="picker"
          label="标签"
          placeholder="点击选择标签"
          @click="showPicker = true"
        />
        <van-popup v-model:show="showPicker" position="bottom">
          <van-picker
            :columns="typeOption"
            @confirm="onConfirm"
            @cancel="showPicker = false"
            title="选择标签"
          />
        </van-popup>
        <van-field
          v-model="timeresult"
          is-link
          readonly
          name="datePicker"
          label="采集时间"
          placeholder="点击选择采集时间"
          @click="showTimePicker = true"
        />
        <van-popup v-model:show="showTimePicker" position="bottom">
          <van-date-picker @confirm="onTimeConfirm" @cancel="showTimePicker = false" v-model="currentDate" title="选择日期" />
        </van-popup>
        <van-uploader :after-read="afterDetailsRead" max-count="1" v-show="!detailsPhotoData" ref="inputImage" class="upload-container">
          <div class="uploader-select-wrapper">
            <van-icon name="plus" color="#458fea" size="24" />
            <span class="font-14 ml-8">上传一张全景图</span>
          </div>
        </van-uploader>
        <div class="upload-container" v-show="detailsPhotoData">
          <div class="uploader-select-wrapper">
            <img class="img-preview" :src="detailsPhotoData.content" alt="">
            <van-icon class="delete-btn" name="cross" color="#458fea" size="22" @click="deleteDetailsPhotoItem()" />
          </div>
        </div>
        <van-field
          v-model="iconname"
          name="图片名称"
          label="图片名称"
          placeholder="图片名称"
          :rules="[{ required: true, message: '请输入图片名称' }]"
        />
      </van-cell-group>
    </div>
  </div>
</template>

<script setup>
import router from "@/router";
import {onBeforeUpdate, onMounted, ref, watch} from "vue";
import {getCurrentDate} from "@/utils";
import api from "@/api";
import {getToken} from "@/utils/token";
import {getOrganizeId} from "@/utils/organizeId";
import {showImagePreview, showToast} from "vant";
import {useRoute} from "vue-router";
import {getUserInfo} from "@/utils/user";
import uuid from 'uuid'
const PbGuid = ref('')
const token = ref('')
const organizeId = ref('')
const route = useRoute()
const iframeUrl = ref('')
const panoname = ref('')
const result = ref({})
const showPicker = ref(false)
const iconname = ref('')
const currentDate = ref([])
const typeOption = ref([])
/**
 * 获取检查类型
 * @returns {Promise<void>}
 */
async function getTypes(){
  const res = await api.GetLabelList({
    Token: token.value,
    OrganizeId: organizeId.value
  })
  if (res.data.Ret === 1) {
   const typeData = res.data.Data
    typeOption.value = []
    for (const label of typeData) {
      typeOption.value.push({
        text: label.LabelName,
        value: label.LabelId
      })
    }
  }
}
const timeresult = ref('');
const showTimePicker = ref(false);
const onConfirm = ({ selectedOptions }) => {
  result.value = selectedOptions[0]?selectedOptions[0]:{};
  showPicker.value = false;
};
const onTimeConfirm = ({ selectedValues }) => {
  timeresult.value = selectedValues.join('-');
  showTimePicker.value = false;
};

const detailsPhotoData = ref('')
const afterDetailsRead = (file) => {
  if (file instanceof Array){
    detailsPhotoData.value = file[0]
  }else {
    detailsPhotoData.value = file
  }
  const filename = file.file.name
  iconname.value = filename.slice(0, filename.lastIndexOf("."))
};
function deleteDetailsPhotoItem(){
  detailsPhotoData.value = ''
  iconname.value = ''
}
/**
 * 左侧点击回退上一级路由
 * 注：replace 方法不会增加路由信息
 */
function onClickLeft(){
  router.back()
}
/**
 * 保存全景图
 */
async function onClickSave(){

  debugger
  if (!PbGuid.value) {
    if(!panoname.value) {
      showToast('请输入全景图集名称!')
      return
    } else if(!result.value.value) {
      showToast('请输入标签!')
      return
    }
  }
  if(!timeresult.value) {
    showToast('请选择采集时间!')
    return
  } else if(!detailsPhotoData.value) {
    showToast('请上传全景图!')
    return
  } else if(!iconname.value){
    showToast('请填写图片名称!')
    return
  }
  const guid = uuid()
  const fd = new FormData
  if (PbGuid.value) {
    fd.append('targetPatchGuid',PbGuid.value)
  }else {
    fd.append('gisinfo','')
    fd.append('pbname',panoname.value)
    fd.append('labelId',result.value.value)
  }
  fd.append('organizeId',organizeId.value)
  fd.append('collectDate',timeresult.value)
  fd.append('pbguid',guid)
  fd.append('willappend','0')
  const filename = detailsPhotoData.value.file.name
  const extension = filename.split('.').pop()
  const name = iconname.value + '.' + extension
  fd.append('Files',detailsPhotoData.value.file,name)
  const res = await api.UploadPanoImages(token.value, fd)
  if (res.data.Ret === 1) {
    router.back()
    showToast(res.data.Msg)
  }else {
    showToast(res.data.Msg)
  }
}
onMounted(()=>{
  token.value = getToken()
  organizeId.value = getOrganizeId()
  PbGuid.value = route.query.PbGuid
  currentDate.value = getCurrentDate()
  getTypes()
})

</script>
<style scoped lang="scss">
.add-container{
  height: 100%;
  .from-container{
    background-color: #F4F5F6;
  }
  .upload-container{
    width: 100%;
    margin-top: 10px;
    padding: 12px;
  }
  :deep(.van-uploader__input-wrapper){
    width: 100%;
  }
  .uploader-select-wrapper{
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 240px;
    border: 1px dashed #458fea;
    border-radius: 2px;
    color: #458fea;
    .img-preview{
      width: 100%;
      height: 100%;
    }
    .delete-btn{
      position: absolute;
      top: 10px;
      right: 10px;
    }
  }
}

</style>

import { createRouter, createWebHashHistory } from 'vue-router'

const routes = [
  {
    path: '/:Token?',
    component:()=> import('../views/Login/index.vue')
  },
  {
    path: '/home',
    component:()=> import('../views/Home/index.vue')
  },
  {
    path: '/agreement',
    component:()=> import('../views/Agreement/index.vue')
  },
  {
    path: '/project',
    component:()=> import('../views/Project/index.vue'),
    children: [
      {
        path: 'model',
        component:()=>import('../views/Project/Model.vue')
      },
      {
        path: 'issue',
        component:()=>import('../views/Project/Issue.vue')
      },
      {
        path: 'pano',
        component:()=>import('../views/Project/Pano.vue')
      },
      {
        path: 'business',
        component:()=>import('../views/Project/Business.vue')
      },
      {
        path: 'document',
        component:()=>import('../views/Project/Document.vue')
      },
      {
        path: 'quality',
        component:()=>import('../views/Project/Quality.vue')
      },
      {
        path: 'safe',
        component:()=>import('../views/Project/Safe.vue')
      },
      {
        path: 'schedule',
        component:()=>import('../views/Project/Schedule.vue')
      },
      {
        path: 'flow',
        component:()=>import('../views/Project/Flow.vue')
      }
    ]
  },
  {
    path: '/model',
    component:()=>import('../views/Project/Model.vue')
  },
  {
    path: '/issue',
    component:()=>import('../views/Project/Issue.vue')
  },
  {
    path: '/addIssue',
    component:()=>import('../views/Issue/addIssue.vue')
  },
  {
    path: '/issueComment',
    component:()=>import('../views/Issue/comment.vue')
  },
  {
    path: '/addComment',
    component:()=>import('../views/Issue/addComment.vue')
  },
  {
    path: '/pano',
    component:()=>import('../views/Project/Pano.vue')
  },
  {
    path: '/panoSubset',
    component:()=>import('../views/Pano/panoSubset.vue')
  },
  {
    path: '/addPano',
    component:()=>import('../views/Pano/addPano.vue')
  },
  {
    path: '/panoPreview',
    component:()=>import('../views/Pano/panoPreview.vue')
  },
  {
    path: '/quality',
    component:()=>import('../views/Project/Quality.vue')
  },
  {
    path: '/safe',
    component:()=>import('../views/Project/Safe.vue')
  },
  {
    path: '/addMission',
    component: () => import('../views/Mission/index.vue'),
    meta: { keepAlive: true}
  },
  {
    path: '/schedule',
    component:()=>import('../views/Project/Schedule.vue')
  },
  {
    path: '/addSchedule',
    component: () => import('../views/Schedule/index.vue'),
    meta: { keepAlive: true}
  },
  {
    path: '/scheduleDetails',
    component: () => import('../views/Schedule/details.vue'),
    meta: { keepAlive: true}
  },
  {
    path: '/flow',
    component:()=>import('../views/Project/Flow.vue')
  },
  {
    path: '/flowFolder',
    component:()=>import('../views/Flow/index.vue')
  },
  {
    path: '/flowDetails',
    component:()=>import('../views/Flow/Details.vue')
  },
  {
    path: '/message',
    component:()=>import('../views/Message/index.vue')
  },
  {
    path: '/setting',
    component:()=>import('../views/Setting/index.vue')
  }
]

const router = createRouter({
  history: createWebHashHistory(),
  routes
})

export default router

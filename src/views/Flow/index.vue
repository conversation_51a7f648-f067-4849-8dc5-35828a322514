<template>
  <div class="children-container">
    <van-sticky>
      <van-nav-bar
          :title="flowData.name"
          left-text="返回"
          left-arrow
          @click-left="onClickLeft"
      />
    </van-sticky>
    <div class="list-container">
        <van-list>
          <div class="item-wrapper" v-for="item in flowData.data" :key="item" @click="clickItem(item)">
            <div class="line-wrapper">
              <van-image
                  class="item-img"
                  width="16px"
                  height="16px"
                  :src="require('../../assets/images/flow/flow-icon.png')"
              />
              <span> {{item.Title}}</span>
            </div>
            <div class="line-wrapper">
              <van-image
                  class="item-img"
                  width="16px"
                  height="16px"
                  :src="require('../../assets/images/flow/flow-creater.png')"
              />
              <span class="span-tittle">发起人：</span>
              <span> {{item.StarterName}}</span>
            </div>
            <div class="line-wrapper" v-if="flowData.type === 'Runing_Init'">
              <van-image
                  class="item-img"
                  width="16px"
                  height="16px"
                  :src="require('../../assets/images/flow/flow-current.png')"
              />
              <span class="span-tittle">当前节点：</span>
              <span> {{item.NodeName}}</span>
            </div>
            <div class="line-wrapper" v-if="flowData.type === 'Runing_Init'">
              <van-image
                  class="item-img"
                  width="16px"
                  height="16px"
                  :src="require('../../assets/images/flow/flow-creater.png')"
              />
              <span class="span-tittle">处理人：</span>
              <span> {{item.TodoEmps.split(',')[1]}}</span>
            </div>
            <div class="line-wrapper" v-if="flowData.type !== 'Runing_Init'">
              <van-image
                  class="item-img"
                  width="16px"
                  height="16px"
                  :src="require('../../assets/images/flow/flow-time.png')"
              />
              <span class="span-tittle">发起时间：</span>
              <span> {{item.RDT}}</span>
            </div>
            <div class="line-wrapper" v-if="flowData.type === 'Runing_Init'">
              <van-image
                  class="item-img"
                  width="16px"
                  height="16px"
                  :src="require('../../assets/images/flow/flow-time.png')"
              />
              <span class="span-tittle">到达时间：</span>
              <span> {{item.RDT}}</span>
            </div>
            <div class="line-wrapper" v-if="flowData.type === 'Complete_Init'">
              <van-image
                  class="item-img"
                  width="16px"
                  height="16px"
                  :src="require('../../assets/images/flow/flow-time.png')"
              />
              <span class="span-tittle">完成时间：</span>
              <span> {{item.SendDT}}</span>
            </div>
          </div>
        </van-list>
    </div>

  </div>
 </template>
<script setup>
import router from "@/router";
import {onMounted, ref} from "vue";

/**
 * 点击返回上一级路由
 */
function onClickLeft() {
  router.back()
}
/**
 * 点击item
 * @param item
 */
function clickItem(item) {
  router.push({
    path:'/flowDetails',
    state:{
      data: JSON.stringify(item)
    }
  })
}
const flowData = ref({})
onMounted(()=>{
  // 获取参数
  let data = window.history.state.data;
  if (data){
    flowData.value = JSON.parse(data)
  }
  console.log('data',flowData.value)

})
</script>

<style scoped lang="scss">
.children-container{
  height: 100%;
  background-color: #f5f7fb;
  .list-container{
    padding-top: 10px;
    background-color: #f5f7fb;
    .item-wrapper{
      border-radius: 8px;
      background-color: white;
      margin-left: 16px;
      margin-right: 16px;
      margin-bottom: 16px;
      padding-bottom: 10px;
      display: flex;
      flex-direction: column;
      position: relative;
      .line-wrapper{
        padding-top: 10px;
        display: flex;
        align-items: center;
        position: relative;
        .item-img{
          margin-left: 20px;
          margin-right: 20px;
        }
        span{
          font-size: 14px;
          font-weight: 500;
        }
        .span-tittle{
          font-size: 14px;
          font-weight: 500;
          color: #666666;
        }
      }
    }
  }

}
</style>
<template>
  <van-sticky>
    <van-nav-bar
        v-if="false"
        :title="projectName"
        left-text="返回"
        left-arrow
        @click-left="onClickLeft"
    />
  </van-sticky>
  <router-view/>
    <van-tabbar @change="onChange" route v-model="tabIndex">
      <van-tabbar-item replace to="/project/document">
        <span>文档</span>
        <template #icon="props">
          <img :src="props.active ? qualityIcon.active : qualityIcon.inactive"  alt=""/>
        </template>
      </van-tabbar-item>
      <van-tabbar-item replace to="/project/model">
        <span>模型</span>
        <template #icon="props">
          <img :src="props.active ? safeIcon.active : safeIcon.inactive"  alt=""/>
        </template>
      </van-tabbar-item>
      <van-tabbar-item replace to="/project/issue">
        <span>协作</span>
        <template #icon="props">
          <img :src="props.active ? issueIcon.active : issueIcon.inactive"  alt=""/>
        </template>
      </van-tabbar-item>
      <van-tabbar-item replace to="/project/pano">
        <span>全景图</span>
        <template #icon="props">
          <img :src="props.active ? scheduleIcon.active : scheduleIcon.inactive"  alt=""/>
        </template>
      </van-tabbar-item>
      <van-tabbar-item replace to="/project/business">
        <span>业务</span>
        <template #icon="props">
          <img :src="props.active ? flowIcon.active : flowIcon.inactive"  alt=""/>
        </template>
      </van-tabbar-item>
    </van-tabbar>

</template>

<script setup>
import {onMounted, ref} from "vue";
import {useRoute} from "vue-router";
import router from "@/router";
import {getOrganizeId, setOrganizeId} from "@/utils/organizeId";
import {getTabIndex, setTabIndex} from "@/utils/tabIndex";
import {getProjectName} from "@/utils/projectName";
const route = useRoute()
const projectName = ref('')
const projectId = ref('')
const flowIcon = {
  active: require('../../assets/images/common/flow-selected.png'),
  inactive: require('../../assets/images/common/flow-noraml.png')
};
const qualityIcon = {
  active: require('../../assets/images/common/quality-selected.png'),
  inactive: require('../../assets/images/common/quality-normal.png')
};
const safeIcon = {
  active: require('../../assets/images/common/safe-selected.png'),
  inactive: require('../../assets/images/common/safe-noraml.png')
};
const scheduleIcon = {
  active: require('../../assets/images/common/schedule-selected.png'),
  inactive: require('../../assets/images/common/schedule-normal.png')
};
const settingIcon = {
  active: require('../../assets/images/common/setting-selected.png'),
  inactive: require('../../assets/images/common/setting-noraml.png')
};
const issueIcon = {
  active: require('../../assets/images/common/setting-selected.png'),
  inactive: require('../../assets/images/common/setting-noraml.png')
};

let isBusiness = ref(false)
let tabIndex = ref(0)
onMounted(()=>{
  tabIndex.value = parseInt(getTabIndex())
  isBusiness.value = window.IP_CONFIG.IS_BUSINESS
  projectName.value = getProjectName()
  projectId.value = getOrganizeId()
  if (tabIndex.value === 0){
    router.replace({
      path: '/project/document'
    })
  }
})

/**
 * 左侧点击回退上一级路由
 * 注：replace 方法不会增加路由信息
 */
function onClickLeft(){
  router.back()
}
/**
 * tab切换方法
 */
const onChange = (index) => {
  setTabIndex(index)
}
onMounted(()=> {
})
</script>


<style scoped lang="scss">
</style>
<template>
  <div class="add-container">
    <van-sticky>
      <van-nav-bar
        title="三维浏览"
        left-text="返回"
        left-arrow
        @click-left="onClickLeft"
      />
    </van-sticky>
    <div class="preview-container">
      <iframe class="preview" :src="props.url" frameborder="0"></iframe>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'
const props = defineProps({
  url: {
    type: String,
  }
})
/**
 * 左侧点击回退上一级路由
 * 注：replace 方法不会增加路由信息
 */
const emit = defineEmits(['closeDialog'])
function onClickLeft(){
  emit('closeDialog')
}
</script>
<style scoped lang="scss">
.add-container{
  position: fixed;
  z-index: 999;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #FFF;
  .preview-container{
    height: calc(100% - 46px);
    .preview{
      width: 100%;
      height: 100%;
    }
  }
}
</style>

<template>
    <div class="progress-content">
        <span class="progress-value" :style="{width: `${props.progress}%`, background: props.background}"></span>
        <span class="progress-val" :style="styleObject">{{props.progress + '%'}}</span>
    </div>
</template>
<script setup>
import {defineProps, reactive} from 'vue'
const props = defineProps({
  background: String,
  progress: Number
})
const styleObject = reactive({
  left: `${props.progress}%`,
  transform: props.progress > 25 ? 'translate(-100%, -50%)' : 'translate(0, -50%)'
})
</script>
<style lang="scss" scoped>
.progress-content{
    position: relative;
    width: 100%;
    height: 100%;
    background-color: #B2DFFF;
    border-radius: 0px 8px 8px 0px;
    .progress-value{
        position: absolute;
        left: 0;
        // width: 25.33%;
        height: 100%;
        border-right: 4px solid #FFF;
        background: linear-gradient(90deg, #809FFF 0%, #4D79FF 100%);
        transform-origin: 0;
        transition: .3s;
    }
        .progress-val{
        position: absolute;
        top: 50%;
        // left: 25.33%;
        // transform: translate(-100%, -50%);
        font-weight: bold;
        font-size: 30px;
        font-family: DINAlternate-Bold, DINAlternate;
        background-color: rgba(255, 255, 255, 0);
        padding: 0 10px;
        color: #FFF;
        text-align: center;
        transition: .3s;
    }
}

</style>
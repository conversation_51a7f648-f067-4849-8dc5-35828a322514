export default {
    state: {
        companyName: '公有云服务',
        companyLogo: '',
        companyInfo: []
    },
    mutations: {
        updateCompanyInfo(state, companyInfo) {
            if (companyInfo.length > 0) {
                state.companyInfo = companyInfo
            }
        },
        updateCompanyName(state,name) {
            if (name) {
                state.companyName = name
            }
        },
        updateCompanyLogo(state,url) {
            if (url) {
                state.companyLogo = url
            }else {
                state.companyLogo = ''
            }
        }
    }
}
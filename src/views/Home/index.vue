<template>
  <div class="home-container">
    <van-sticky>
      <div class="list-tittle">
        <van-image
            @click="gotoSetting"
            class="setting-icon"
            width="20"
            height="20"
            :src="require('../../assets/images/common/home-setting-icon.png')">
        </van-image>
        <van-search
            class="van-search"
            v-model="searchValue"
            placeholder="请输入搜索关键词"
            @search="getList"
            @cancel="cancelSearch"
        />
        <div class="type-name">
          <van-image
              @click="gotoMessage"
              class="van-image"
              width="20"
              height="20"
              :src="require('../../assets/images/common/message-icon.png')"
          />
          <div v-if="isHasMessage" class="red-dot"></div>
        </div>

      </div>
    </van-sticky>
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
      <van-list
          v-model:loading="loading"
          :finished="finished"
          @load="onLoad"
      >
        <div class="item-container" v-for="item in projectList" :key="item" @click="goToMainProjectView(item)">
          <van-image
              class="project-img"
              :src="item.Thumbnail"
          />
          <div class="item-right-container">
            <span>{{item.ProjectName}}</span>
            <span>{{item.OrganizeName}}</span>
            <span>{{splitTime(item.ExpiryTime)}}</span>
<!--            <span>{{item.Manager}}</span>-->
          </div>
        </div>
      </van-list>
    </van-pull-refresh>
  </div>
</template>

<script setup>
import CryptoJS from 'crypto-js'
import { ref, onMounted } from 'vue';
import {getToken, setToken} from '@/utils/token.js'
import {showToast} from "vant";
import {useRouter, useRoute} from "vue-router";
import {setOrganizeId} from "@/utils/organizeId";
import {setOrganizationId} from "@/utils/OrganizationId";
import {setUserInfo} from "@/utils/user";
import {setAu} from "@/utils/au";
import api from "@/api";
import {setTabIndex} from "@/utils/tabIndex";
import {setProjectNam} from "@/utils/projectName";
const router = useRouter()
const searchValue = ref('')
let loading = ref(false);
const finished = ref(false);
const refreshing = ref(false);
let projectList= ref([])
const PublicKey = 'MK4ZJF10PRO19*#8'
/**
 * 对称加密算法
 * @param json
 * @param CryptoJS
 * @returns {number|string}
 * @constructor
 */
function SymmetricEncryption(json, CryptoJS) {
  //json.input   输入的明文
  //json.key     加密密钥
  //return value 正常时返回：加密后的密码，异常时返回-1
  if (!json || !json.input || !json.key) {
    console.warn("json, json.input and json.key are not allow null!");
    return -1;
  }
  // 由于 key 小于16位会有问题，需要额外处理 key
  if (json.key.length === 0) {
    console.warn("json.length should not be zero!");
    return -1;
  }
  while (json.key.length < 16) {
    json.key = json.key + json.key;
  }
  const KEY = json.key; //32位
  const IV = "*BIM19FF4KMY0R8*"; //16位
  const key = CryptoJS.enc.Utf8.parse(KEY);
  const iv = CryptoJS.enc.Utf8.parse(IV);

  let encrypted = "";

  const srcs = CryptoJS.enc.Utf8.parse(json.input);
  encrypted = CryptoJS.AES.encrypt(srcs, key, {
    iv: iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
  });
  return encrypted.ciphertext.toString();
}

/**
 * 路由跳转到主界面
 * @param item
 */
function goToMainProjectView(item) {
  setProjectNam(item.ProjectName)
  setOrganizationId(item.OrganizeId)
  setTabIndex(0)
  setOrganizeId(item.ProjectId)
  getAu(item.ProjectId)
  router.push({
    path: '/project'
  })
}
/**
 * 获取权限数据
 */
async function getAu(organizeId) {
  let isAdd = false
  let isAudit = false
  const res = await api.GetUserMenuTree({
    Token: getToken(),
    organizeId,
    parentId: '0'
  })
  if (res.data.Ret === 1){
    const data = res.data.Data
    for (const datum of data) {
      if (datum.MenuCode === 'JDGL'){
        for (const datumElement of datum.Children) {
          if (datumElement.MenuCode === 'JDTB'){
            for (const datumElementElement of datumElement.Buttons) {
              if (datumElementElement.ButtonCode === 'JDTB_SubmitAudit'){
                // 可以填报
                isAdd = true
              }
              if (datumElementElement.ButtonCode === 'JDTB_Audit'){
                // 可以审核
                isAudit = true
              }
            }

          }
        }
      }
    }
    let au = false
    if (isAudit){
      au = false
    }
    if (isAdd){
      au = true
    }
    // 权限数据存储
    setAu(au)
  }
}
/**
 * 路由跳转到消息
 */
function gotoMessage(){
  router.push({
    path: '/message',
    query: {
    }})
}
/**
 * 路由跳转到设置
 */
function gotoSetting(){
  router.push({
    path: '/setting',
    query: {
    }})
}
/**
 * 过期时间字段字符串截取拼接
 * @param time
 * @returns {string}
 */
function splitTime(time) {
  if (time){
    return  time.split(' ')[0]+' 到期'
  }else {
    return'无到期时间'
  }
}
/**
 * 刷新列表
 */
function onRefresh() {
  // 清空列表数据
  finished.value = false;
  // 重新加载数据
  // 将 loading 设置为 true，表示处于加载状态
  loading.value = true;
  // 加载数据
  getList();
  // 取消刷新状态
  refreshing.value = false
}
/**
 * 取消搜索
 */
function cancelSearch(){
  searchValue.value=''
  getList()
}
/**
 * 列表加载方法
 */
function onLoad(){
  // login()
  getList()
}
/**
 * 获取项目列表（搜索）
 * @returns {Promise<void>}
 */
async function getList() {
  loading.value = true
  const res = await api.GetProjectList({
    token: getToken(),
    pageNum: 1,
    pageSize: 99999,
    keyword: searchValue.value
  })
  if (res.data.Ret === 1) {
    projectList.value = res.data.Data.rows
  } else {
    showToast(res.data.Msg)
  }
  finished.value = true
  loading.value = false
}
/**
 * 自动登录
 * @returns {Promise<void>}
 */
async function login() {
  const res = await api.Login({
    IfSingleLogin: '',
    // UserName: window.IP_CONFIG.USERNAME,
    // Password: SymmetricEncryption({input: window.IP_CONFIG.PASSWORD + PublicKey, key: PublicKey}, CryptoJS)
  })
  if (res.data.Ret === 1) {
    // 保存token
    setToken(res.data.Data.token)
    // 获取用户信息
    await getUserInfo(res.data.Data.token)
    // 获取列表
    await getList()
  }
}
/**
 * 获取用户信息并保存
 * @param token
 */
async function getUserInfo(token) {
  const res = await api.GetUser({
    token
  })
  if (res.data.Ret === 1){
    setUserInfo(JSON.stringify(res.data.Data))
  }
}
onMounted(() => {
  // 禁用双指放大
  document.documentElement.addEventListener('touchstart', function (event) {
      if (event.touches.length > 1) {
          event.preventDefault();
      }
  }, {
      passive: false
  });
 
  // 禁用双击放大
  let lastTouchEnd = 0;
  document.documentElement.addEventListener('touchend', function (event) {
      let now = Date.now();
      if (now - lastTouchEnd <= 300) {
          event.preventDefault();
      }
      lastTouchEnd = now;
  }, {
      passive: false
  });
  const route = useRoute()
  const mToken = route.query.token
  if (mToken) {
    setToken(mToken)
    getUserInfo(mToken)
  }
  getMessages()
})
const isHasMessage = ref(false)
/**
 * 获取消息列表
 */
async function getMessages() {
  const result = await api.ListApp({
    HasRead: false,
    Module: '',
    Token: getToken()
  })
  if (result.data.Ret === 1){
    if (result.data.Data.length> 0 ){
      isHasMessage.value = true
    }
  }
}
</script>

<style scoped lang="scss">
.home-container{
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: white;
  .list-tittle{
    display: flex;
    align-items: center;
    background-color: #FFF;
    padding: 10px 0;
    .setting-icon{
      margin-left: 10px;
    }
    .van-search{
      width: calc(100% - 60px);
    }
    .type-name{
      position: relative;
      .van-image{
        width: 30px;
      }
      .red-dot::before {
        content: "";
        position: absolute;
        top:1px;
        right: 2px;
        width: 5px;
        height: 5px;
        background-color: #f00;
        border-radius: 50%;
      }
    }

  }
  .van-pull-refresh{
    flex: 1;
    overflow: auto;
  }
  .item-container{
    margin-left: 16px;
    margin-right: 16px;
    margin-bottom: 16px;
    display: flex;
  }
  .project-img{
    width: 100px;
    height: 90px;
    border-radius: 4px;
    overflow: hidden;
  }
  .item-right-container{
    margin-left: 16px;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    :first-child{
      font-size: 16px;
      color: rgba(40, 58, 79, 1);
      font-weight: 500;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      line-height: 1.4;
    }
    :nth-child(2){
      margin-top: auto;
      font-size: 13px;
      font-weight: 400;
      color: rgba(40, 58, 79, 1);
    }
    :nth-child(3){
      font-size: 13px;
      font-weight: 400;
      color: rgba(97, 111, 125, 1);
    }
    :last-child{
      font-size: 13px;
      font-weight: 400;
      color: rgba(97, 111, 125, 1);
    }
  }
}
</style>

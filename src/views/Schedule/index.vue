<template>
  <div class="add-container">
    <van-sticky>
      <van-nav-bar
          :title="tittle"
          left-text="返回"
          left-arrow
          @click-left="onClickLeft"
      />

    </van-sticky>

    <!--任务 start-->
    <van-popup
        class="hide-overflow"
        v-model:show="isShowTasks"
        position="bottom"
        lock-scroll
        :style="{ height: '90%' }"
    >
      <van-sticky>
        <div class="header-container">
          <img
              width="25"
              height="25"
              :src="require('../../assets/images/common/back-arrow.png')"
              @click="closeTaskPop('close')"
              alt=""/>
          <span>任务选择</span>
          <span @click="closeTaskPop('confirm')">确认</span>
        </div>
      </van-sticky>
      <van-radio-group  v-model="taskChecked" class="mt-10" style="height:100%;overflow-y:scroll">
        <van-cell-group>
          <van-cell
              v-for="(item, index) in taskData"
              clickable
              :key="index"
              :title="item.Name"
              @click="toggleTask(index,item)"
          >
            <template #icon>
              <img
                  class="left-img"
                  v-if="!item.ParentTaskUID"
                  width="25"
                  height="25"
                  :src="require('../../assets/images/quality/element-folder.png')" alt="">
              <img
                  class="left-img"
                  v-else
                  width="25"
                  height="25"
                  :src="require('../../assets/images/quality/task-item.png')" alt="">
            </template>
            <template #right-icon>
              <img
                  v-if="item.children"
                  class="right-img"
                  width="15"
                  height="15"
                  :src="require('../../assets/images/common/right-arrow.png')" alt="">

              <van-radio
                  v-else
                  :name="item"
                  :ref="el => checkboxTaskRefs[index] = el"
                  @click.stop
              />
            </template>
          </van-cell>
        </van-cell-group>
      </van-radio-group>

    </van-popup>
    <!--任务 end-->

    <!-- 填报日期 start-->
    <van-popup v-model:show="isShowCreatePicker" position="bottom">
      <van-date-picker @confirm="onConfirmCreateTime" @cancel="isShowCreatePicker = false" v-model="currentDate" title="请选择填报日期"/>
    </van-popup>
    <!-- 填报日期 end-->

    <!-- 实际开始时间选择 start-->
    <van-popup v-model:show="isShowStartPicker" position="bottom">
      <van-date-picker @confirm="onConfirmStartTime" @cancel="isShowStartPicker = false" v-model="currentDate" title="请选择实际开始日期"/>
    </van-popup>
    <!-- 实际开始时间选择 end-->

    <!-- 实际结束时间选择 start-->
    <van-popup v-model:show="isShowEndPicker" position="bottom">
      <van-date-picker @confirm="onConfirmEndTime" @cancel="isShowEndPicker = false" v-model="currentDate" title="请选择实际结束日期"/>
    </van-popup>
    <!-- 实际结束时间选择 end-->

    <!--填报信息 start-->
    <div class="form-container mt-10">
      <van-cell-group inset class="mt-10 pt-12">
        <span class="font-weight-500 mt-12 pt-12 ml-10">基本信息</span>
        <div class="form-item">
          <span>任务名称</span>
          <div class="line"></div>
          <div class="bg-right" @click="getAllTree">
            <span>{{taskName}}</span>
          </div>
        </div>
        <div class="form-item">
          <span>计划名称</span>
          <div class="line"></div>
          <div class="bg-right">
            <span>{{planName}}</span>
          </div>
        </div>
        <div class="form-item">
          <span>计划日期</span>
          <div class="line"></div>
          <div class="bg-right">
            <span>{{taskPlanTime}}</span>
          </div>
        </div>
        <div class="form-item">
          <span>呈现方式</span>
          <div class="line"></div>
          <div class="bg-right">
            <van-popover v-model:show="isShowPopover" :actions="actions" @select="onSelect" :trigger="disabledPopover ? 'manual' : 'click'" >
              <template #reference>
                <span>{{type}}</span>
                <van-icon class="right-drop" name="arrow-down" v-if="isShowDown"/>
              </template>
            </van-popover>

          </div>
        </div>
        <div class="form-item" v-if="!isShowExtra && !isProgress">
          <span>{{allTips}}</span>
          <div class="line"></div>
          <div class="bg-right">
            <input v-model="allProgress"  placeholder="请输入" type="number" :disabled="disabledPopover"/>
          </div>
        </div>
        <div class="form-item-item mt-12 pt-12 ml-10 mr-10" v-if="isShowExtra">
          <span class="ml-10">任务高程</span>
          <div class="item">
            <span class="ml-10">底部：</span>
            <div class="bg-right">
              <input v-model="lowTotal"  placeholder="请输入底部高程" :disabled="disabledPopover"/>
            </div>
          </div>
          <div class="line-2"></div>
          <div class="item">
            <span class="ml-10">顶部：</span>
            <div class="bg-right">
              <input v-model="allProgress"  placeholder="请输入顶部高程" :disabled="disabledPopover"/>
            </div>
          </div>
        </div>

      </van-cell-group>
      <van-cell-group inset class="mt-10 pt-12 pb-12">
        <span class="font-weight-500 mt-12 pt-12 ml-10">计划任务完成情况</span>
        <div class="form-item">
          <span>任务状态</span>
          <div class="line"></div>
          <div class="bg-right">
            <span>{{state}}</span>
          </div>
        </div>

        <div class="form-item">
          <span>填报日期</span>
          <div class="line"></div>
          <div class="bg-right" @click="isShowCreatePicker = true">
            <span>{{createTime}}</span>
            <van-image
                class="date-select"
                width="20"
                height="20"
                :src="require('../../assets/images/schedule/date-select.png')"
            ></van-image>
          </div>

        </div>
        <div class="form-item-item mt-12 pt-12 ml-10 mr-10">
          <span class="ml-10">实际日期</span>
          <div class="item">
            <span class="ml-10">开始：</span>
            <div class="bg-right" @click="openTimePicker('start')">
              <span>{{actStartTime}}</span>
            </div>
            <van-image
                @click="openTimePicker('start')"
                class="date-select"
                width="20"
                height="20"
                :src="require('../../assets/images/schedule/date-select.png')"
            ></van-image>
          </div>
          <div class="line-2"></div>
          <div class="item">
            <span class="ml-10">结束：</span>
            <div class="bg-right" @click="openEndTimePicker()">
              <span>{{actEndTime}}</span>
            </div>
            <van-image
                @click="openEndTimePicker()"
                class="date-select"
                width="20"
                height="20"
                :src="require('../../assets/images/schedule/date-select.png')"
            ></van-image>
          </div>
        </div>
        <div class="form-item-item mt-12 pt-12 ml-10 mr-10">
          <span class="ml-10">完成比例</span>
          <div class="item">
            <span class="ml-10">计划：</span>
            <div class="bg-right">
              <span >{{m_adding_planvalue}}</span>
              <span class="right-unit">{{unitType}}</span>
            </div>
          </div>
          <div class="line-2"></div>
          <div class="item">
            <span class="ml-10">实际：</span>
            <div class="bg-right">
              <input v-model="actProgress"  placeholder="请输入"  @change="evt_percentchange" type="number" :disabled="disableActProgress"/>
              <span>{{unitType}}</span>
            </div>
          </div>
        </div>
      </van-cell-group>
      <van-cell-group inset class="mt-10 pt-12 pb-12">
        <span class="font-weight-500 ml-10 upload-container">进度形象照片</span>
        <van-uploader
            class="ml-10 mt-10"
            v-model="fileList"
            max-count="9"
            :deletable="true"
            :disabled="false"
        />
      </van-cell-group>
      <!--保存 直接提交-->
      <div class="func-wrapper" v-if="isShowFunc">
        <div class="save" @click="onClickRight(false)">
          <span>保存</span>
        </div>
        <div class="add"  @click="onClickRight(true)">
          <span>直接提交</span>
        </div>
      </div>
    </div>
    <!--填报信息 end-->
  </div>
</template>

<script setup>
import router from "@/router";
import {computed, onMounted, ref, watch} from "vue";
import api from "@/api";
import {getToken} from "@/utils/token";
import {getOrganizeId} from "@/utils/organizeId";
import {useRoute} from "vue-router";
import {getCurrentDate, getCurrentTime} from "@/utils";
import {isNull} from "@/utils/ObjectIsNull";
import {splitTime} from "@/utils/timeSplit";
import {getUserInfo} from "@/utils/user";
import {showToast} from "vant";
import {getAu} from "@/utils/au";

const allProgress = ref(0)
const createTime = ref('')
const actStartTime = ref('请选择日期')
const actEndTime = ref('请选择日期')
const isShowStartPicker = ref(false)
const isShowEndPicker = ref(false)
const isShowCreatePicker = ref(false)
const planProgress= ref(0)
const actProgress= ref(0)
const state = ref('状态')
const lowTotal = ref(0)
const isProgress = ref(true)
const isShowDown = ref(false)
const isShowFunc = ref(true)
/**
 * 文件选择后
 * @param file
 */
const photoData = ref([])
const fileList = ref([])
const photoFiles = ref([])
const afterRead = (file) => {
  const files = []
  if (file instanceof Array){
    for (const temp of file) {
      files.push(temp.file)
      photoFiles.value.push(temp.file)
      photoData.value.push(temp.content)
    }
  }else {
    files.push(file.file)
    photoFiles.value.push(file.file)
    photoData.value.push(file.content)
  }
  console.log('photoData',photoData.value)
  console.log('photoFiles',photoFiles.value)
};

/**
 * 打开实际时间选择
 * @param type
 */
function openTimePicker(type){
  if (!disabledPopover.value){
    if (type === 'start'){
      isShowStartPicker.value = true
    }else {
      isShowEndPicker.value = true
    }
  }
}

/**
 * 打开实际时间选择
 * @param type
 */
function openEndTimePicker(){
  if (!disabledEndTimePopover.value){
      isShowEndPicker.value = true
  }
}

/**
 * 填报日期确认
 * @param selectedValues
 */
const onConfirmCreateTime = ({ selectedValues }) => {
  createTime.value = selectedValues.join('-');
  isShowCreatePicker.value = false;
  computeTimeDiff()
}
/**
 * 开始日期确认
 * @param selectedValues
 */
const onConfirmStartTime = ({ selectedValues }) => {
  console.log('sss',selectedValues)
  actStartTime.value = selectedValues.join('-');
  isShowStartPicker.value = false;
}
const disableActProgress = ref(false)
/**
 * 结束日期确认
 * @param selectedValues
 */
const onConfirmEndTime = ({ selectedValues }) => {
  actProgress.value = allProgress.value
  disableActProgress.value = true
  actEndTime.value = selectedValues.join('-');
  isShowEndPicker.value = false;
}
/**
 * 左侧点击回退上一级路由
 * 注：replace 方法不会增加路由信息
 */
function onClickLeft(){
  router.back()
}

/**
 * 按钮
 * @param boo
 */
function onClickRight(boo){
  isAudit.value = boo
  setUnitText()
}
const taskChecked = ref({});
const isShowTasks = ref(false)

const taskName = ref('请选择任务')

const taskPlanTime = computed(() => {
  if (!isNull(taskChecked.value)){
    computeTimeDiff()
    let s = splitTime(taskChecked.value.Start)
    let e = splitTime(taskChecked.value.Finish)
    return s + ' 至 ' + e
  }
  return '根据任务自动获取'
})
/**
 * 关闭任务弹窗
 */
function closeTaskPop(type) {
  switch (type){
    case 'close':
      taskChecked.value = {}
      break
    case 'confirm':
      // 修改任务
      console.log('item',taskChecked.value)
      taskName.value = taskChecked.value.Name
      getProject(taskChecked.value.UID,taskChecked.value.ProjectUID)
      getUnitTextConfig()
      break

  }
  isShowTasks.value = false
}

const checkboxTaskRefs = ref([]);
const planName = ref('根据任务自动获取')
/**
 * 任务选择
 * @param index
 * @param item
 */
const toggleTask = (index,item) => {
  if (!item.ProjectUID){
    planName.value = item.Name
  }
    if (taskData.value[index].children){
      taskData.value = taskData.value[index].children
    }else {
      taskChecked.value = item
    }
};

const unitType = ref('%')
const isShowPopover = ref(false);
const actions = [
  { text: '百分比%' },
  { text: '里程m' },
  { text: '里程km' },
  { text: '高程m' }
];
const isShowExtra = ref(false)
const type = ref('请选择')
const allTips = ref('任务总量')
/**
 * 单位选择
 * @param action
 */
function onSelect(action){
  type.value = action.text
  switch (action.text){
    case '百分比%':
      allProgress.value = 100
      unit.value.UnitType = 1
      allTips.value = '任务总量'
      planProgress.value = 100
      actProgress.value = 0
      isShowExtra.value = false
      isProgress.value = true
      unitType.value = action.text.charAt(action.text.length - 1)
      break
    case '里程m':
      allProgress.value = 0
      unit.value.UnitType = 2
      allTips.value = '任务里程'
      planProgress.value = 0
      actProgress.value = 0
      isShowExtra.value = false
      isProgress.value = false
      unitType.value = action.text.charAt(action.text.length - 1)
      break
    case '里程km':
      allProgress.value = 0
      unit.value.UnitType = 3
      allTips.value = '任务里程'
      planProgress.value = 0
      actProgress.value = 0
      isShowExtra.value = false
      isProgress.value = false
      unitType.value = 'km'
      break
    case '高程m':
      allProgress.value = 0
      lowTotal.value = 0
      unit.value.UnitType = 4
      allTips.value = '任务里程'
      planProgress.value = 0
      actProgress.value = 0
      isShowExtra.value = true
      isProgress.value = false
      unitType.value = action.text.charAt(action.text.length - 1)
      break
  }
}

let loading = ref(false);
const taskData = ref([])

/**
 * 获取全部计划
 */
async function getAllTree() {
  isShowTasks.value = true
  loading.value = true
  const res = await api.GetTree({
    Token: getToken(),
    OrganizeId: getOrganizeId(),
  })
  if (res.data.Ret === 1){
    taskData.value = res.data.Data
  }
  loading.value = false
}

const unit = ref({
  UnitType: 1
})
const isFirst = ref(false)
const disabledPopover = ref(true)
const disabledEndTimePopover = ref(false)

/**
 * 获取单位
 * @returns {Promise<void>}
 */
async function getUnitTextConfig() {
  const res = await api.GetUnitTextConfig({
    Token: getToken(),
    uid: taskChecked.value.UID,
  })
  if (res.data.Ret === 1) {
    if (JSON.stringify(res.data.Data) !== '{}') {
      unit.value = res.data.Data
      // 不为null时已经填报过
      if (unit.value.UnitType) {
        switch (unit.value.UnitType) {
          case 1:
            isProgress.value = true
            allTips.value = '任务总量'
            planProgress.value = unit.value.UnitValue
            allProgress.value = unit.value.UnitValue
            type.value = '百分比%'
            unitType.value = '%'
            break
          case 2:
            isProgress.value = false
            allTips.value = '任务里程'
            planProgress.value = unit.value.UnitValue
            allProgress.value = unit.value.UnitValue
            type.value = '里程m'
            unitType.value = 'm'
            break
          case 3:
            isProgress.value = false
            allTips.value = '任务里程'
            planProgress.value = unit.value.UnitValue
            allProgress.value = unit.value.UnitValue
            type.value = '里程km'
            unitType.value = 'km'
            break
          case 4:
            isProgress.value = false
            isShowExtra.value = true
            planProgress.value = unit.value.UnitValue
            type.value = '高程m'
            unitType.value = 'm'
            lowTotal.value = unit.value.UnitValue.split(',')[0]
            allProgress.value = unit.value.UnitValue.split(',')[1]
            planProgress.value = allProgress.value
            break
        }
      } else {
        isProgress.value = true
        isShowExtra.value = false
        unit.value.UnitType = 1
        planProgress.value = 0
        allProgress.value = 0
        type.value = '百分比%'
        unit.value = '%'
      }
    }
  }
}

/**
 * 获取两个日期对象差值
 * @param {*} sDate1 日期 date对象 或者 字符串
 * @param {*} sDate2   日期 date对象 或者 字符串
 * @returns
 */
function AbsDateDiff(sDate1, sDate2) {
  let aDate, oDate1, oDate2, iDays;
  oDate1 = new Date(sDate1).getTime();
  oDate2 = new Date(sDate2).getTime();
  iDays = parseInt((oDate1 - oDate2) / 1000 / 60 / 60 / 24) //把相差的毫秒数转换为天数
  return  iDays;
}

/**
 * 获取两个日期对象差值
 * @param {*} sDate1 日期 date对象 或者 字符串
 * @param {*} sDate2   日期 date对象 或者 字符串
 * @returns
 */
function DateDiff(sDate1, sDate2) {
  let aDate, oDate1, oDate2, iDays
  oDate1 = new Date(sDate1).getTime()
  oDate2 = new Date(sDate2).getTime()
  iDays = parseInt((oDate1 - oDate2) / 1000 / 60 / 60 / 24) //把相差的毫秒数转换为天数
  return Math.abs(iDays)
}

/**
 * 判断当前日期是否在计划日期范围之内
 * @param _adding
 * @param beginDateStr
 * @param endDateStr
 * @returns {boolean}
 */
function isDuringDate (_adding,beginDateStr, endDateStr) {
  let curDate = new Date(_adding),
      beginDate = new Date(beginDateStr),
      endDate = new Date(endDateStr);
  if (curDate >= beginDate && curDate <= endDate) {
    return true;
  }
  return false;
}
const TimeDiffAdd = ref('')
const m_planpercentStr = ref('')
const m_planpercent = ref(0)
const m_adding_planvalue = ref(0)
const m_addingpercent = ref(0)
const m_adding_actualvalue = ref(0)

/**
 * 进度填报的实际完成进度
 *
 */
function evt_percentchange() {
  m_adding_actualvalue.value = actProgress.value;
  // 超前滞后根据计划完成进度和实际完成进度计算
  if(m_adding_planvalue.value * 1 < m_adding_actualvalue.value * 1){
    state.value = '超前';
  }
  if(m_adding_planvalue.value * 1 > m_adding_actualvalue.value * 1){
    state.value = '滞后';
  }
  if(m_adding_planvalue.value * 1 == m_adding_actualvalue.value * 1){
    state.value = '正常';
  }
  // 百分比：实际完成比例 = 实际完成进度
  // 里程： 实际完成比例 = （实际完成进度/总进度）*100
  // 高程：实际完成比例 = （实际完成进度/顶部高层）*100
  switch (unit.value.UnitType) {
    case 1:
      m_addingpercent.value = m_adding_actualvalue.value;
      break;
    case 2:
    case 3:
      m_addingpercent.value = ((m_adding_actualvalue.value * 1) / (allProgress.value * 1) ) * 100
      break;
    case 4:
      m_addingpercent.value = ((m_adding_actualvalue.value * 1 - lowTotal.value * 1) / (allProgress.value * 1 - lowTotal.value * 1) ) * 100
      break;
  }

}
/**
 * 计算时间差值
 */
function computeTimeDiff(){
  let startPlanTime = taskChecked.value.Start.substr(0,10);
  let endPlanTime = taskChecked.value.Finish.substr(0,10);

  let TimeDiff = DateDiff(startPlanTime,endPlanTime) + 1; // 计划天数
  TimeDiffAdd.value = TimeDiff;

  let TimeDiffPer = DateDiff(startPlanTime,createTime.value);  // 计划开始时间和填报时间的时间差

  let diffdate = isDuringDate(createTime.value,startPlanTime,endPlanTime);  // 查看填报时间是否在开始时间和结束时间内
  let diffPer = (100 / TimeDiff); // 100/计划天数，计算每天的百分比
  let tdiffper = TimeDiffPer * diffPer; // 天数差 * 每天的百分比 = 当前的计划百分比

  if(diffdate){
    let diffLastPer = (diffPer*1 + tdiffper*1 ).toFixed(1);
    if(diffLastPer > 100){
      diffLastPer = 100
    }else{
      diffLastPer = (diffPer*1 + tdiffper*1 ).toFixed(1);
    }
    // 计算计划完成比例
    m_planpercentStr.value = diffLastPer + '%';
    m_planpercent.value = diffLastPer * 1;
    planProgress.value = diffLastPer * 1
    // 在当前开始-结束时间范围内=计算计划完成进度
    // 百分比：计划完成进度 = 计划完成比例 _this.m_planpercent
    // 里程：计划完成进度 = 总里程数*（计划完成比例/100）  方便计算，直接拿计划完成比例计算了
    // 高程：计划完成进度 = （（（顶部高程-底部高程）/ 总计划天数）* 当前第几天）+ 底部高程
    // （（顶部高层-底部高层）/（（当前天数进度高层+底部高层））*100%
    state.value = toCalculatePercentState(m_planpercent.value,actProgress.value)
    console.log('unit',unit.value.UintType)
    switch (unit.value.UnitType) {
      case 1:
        m_adding_planvalue.value = m_planpercent.value
        planProgress.value = m_adding_planvalue.value
        break;
      case 2:
      case 3:
        m_adding_planvalue.value = (allProgress.value * (m_planpercent.value / 100)).toFixed(2);
        planProgress.value = m_adding_planvalue.value
        break;
      case 4:
        let _val = ((Number(allProgress.value) - Number(lowTotal.value)) / TimeDiff) * (TimeDiffPer + 1) * 1;
        m_adding_planvalue.value = (_val * 1 + lowTotal.value * 1).toFixed(2)
        planProgress.value = m_adding_planvalue.value
        break;
    }
  }else{
    if(AbsDateDiff(createTime.value,startPlanTime) < 1){
      m_planpercentStr.value = '0%';
      m_planpercent.value = 0;
      state.value = '超前';
      switch (unit.value.UnitType) {
        case 1:
          m_adding_planvalue.value = 0;
          break;
        case 2:
        case 3:
          m_adding_planvalue.value = 0;
          break;
        case 4:
          m_adding_planvalue.value  = lowTotal.value * 1
          break;
      }
    }else{
      m_planpercentStr.value = '100%';
      m_planpercent.value = 100
      state.value = '滞后';
      switch (unit.value.UnitType) {
        case 1:
          m_adding_planvalue.value = 100;
          break;
        case 2:
        case 3:
          m_adding_planvalue.value = allProgress.value
          break;
        case 4:
          m_adding_planvalue.value = allProgress.value * 1
          break;
      }
    }
  }
}

/**
 * 计算状态
 * @param planPercent
 * @param actualPercent
 * @returns {string}
 */
function toCalculatePercentState(planPercent,actualPercent){
  let setToleranceValues = 0;
  // 目前没有容差值，setToleranceValues值先为0
  // 当实际完成时间在计划时间范围内使用该方法
  // 当实际比例在planPercent ± setToleranceValues内，属于正常,小于属于滞后，大于属于超前
  let percentState = '';
  if(actualPercent >= planPercent - setToleranceValues  && actualPercent <= planPercent+setToleranceValues){
    percentState = '正常';
  }else if(actualPercent > planPercent - setToleranceValues){
    percentState = '超前';
  }else{
    percentState = '滞后';
  }
  return percentState;
}
const tableData = ref([])
/**
 * 获取计划有无数据
 */
async function getProject(uid,projectId) {
  const res = await api.GetScheduleProject({
    Token: getToken(),
    uid,
    projectid: projectId
})
  if (res.data.Ret === 1) {
    tableData.value = res.data.Data.baseProgressNewOutputs
    isFirst.value = tableData.value.length <= 0;
    if (!isFirst.value){
      isShowDown.value = false
      disabledPopover.value = true
      actStartTime.value =  tableData.value[0].Progress_actualstarttime.slice(0,10)
      if (tableData.value[0].Progress_actualendtime){
        actEndTime.value =  tableData.value[0].Progress_actualendtime.slice(0,10)
        disabledEndTimePopover.value = true
        setProject(tableData.value[0])
      }else {
        disabledEndTimePopover.value = false
        setUncompletedProject(tableData.value[0])
      }
      if (tableData.value[0].Progress_actualratio === 100){
        // 实际进度为100
        isShowFunc.value = false
      }else {
        isShowFunc.value = true
      }
    }else {
      isShowFunc.value = true
      disabledPopover.value = false
      isShowDown.value = true
      recoverProject(tableData.value[0])
    }
  }
}
/**
 * 已完成数据设置显示
 */
async function setProject(project) {
  state.value = project.Progress_state
  createTime.value = project.Progress_unittime.split(' ')[0]
  m_adding_planvalue.value = project.Progress_planvalue
  actProgress.value = project.Progress_actualvalue
}
/**
 * 未完成数据设置显示
 */
async function setUncompletedProject(project) {
  createTime.value = getCurrentTime().split(' ')[0]
  actEndTime.value = '请选择日期'
  actProgress.value = 0
}
/**
 * 无数据设置显示
 */
async function recoverProject(project) {
  createTime.value = getCurrentTime().split(' ')[0]
  actStartTime.value = '请选择日期'
  actEndTime.value = '请选择日期'
  actProgress.value = 0
}
const user = JSON.parse(getUserInfo())
/**
 * 新增进度填报
 * @returns {Promise<void>}
 */
async function setUnitText() {
  let value = ''
  if (unit.value.UnitType === 4){
    value = lowTotal.value + ','+ allProgress.value
  }else {
    value = allProgress.value + ''
  }
  const res = await api.SetUnitText({
    Token: getToken(),
    UnitType: unit.value.UnitType + '',
    UnitValue: value,
    UID: taskChecked.value.UID,
  })
  if (res.data.Ret === 1){
   await add()
  }
}

const isAudit = ref(false)
/**
 * 新增进度填报
 * @returns {Promise<void>}
 */
async function add() {
  // 组装图片数据
  const form = new FormData
  form.append('token',getToken());
  form.append('Progress_PlanId',taskChecked.value.ProjectUID)
  form.append('Progress_Name',taskChecked.value.Name)
  form.append('Progress_state',state.value)
  form.append('Progress_createuser',user.RealName)
  form.append('Progress_createuserid',user.UserId)
  form.append('Progress_planstarttime',taskChecked.value.Start)
  form.append('Progress_plannendtime',taskChecked.value.Finish)
  if (actStartTime.value === '请选择日期'){
    showToast('请填写实际开始日期')
    return
  }
  form.append('Progress_actualstarttime',actStartTime.value)
  if (actEndTime.value !== '请选择日期'){
    form.append('Progress_actualendtime',actEndTime.value)
  }
  form.append('Progress_planfate',TimeDiffAdd.value)
  form.append('Progress_treeID',taskChecked.value.UID)
  form.append('Progress_ProjectID',taskChecked.value.ProjectUID)
  form.append('Progress_parentid',taskChecked.value.ParentTaskUID)
  form.append('Progress_unittime',createTime.value)
  form.append('IsSubmitAudit',false)
  form.append('Progress_planvalue',Number.parseFloat(m_adding_planvalue.value))
  if (actEndTime.value !=='请选择日期'){
    form.append('Progress_actualratio',100)
    form.append('Progress_planratio',100)
    // 分类设置
    form.append('Progress_actualvalue',Number.parseFloat(allProgress.value))
  }else {
    form.append('Progress_planratio',m_planpercent.value)
    form.append('Progress_actualratio',m_addingpercent.value)
    form.append('Progress_actualvalue',actProgress.value)
  }
  if (Number(m_addingpercent.value) === 100){
    if (actEndTime.value ==='请选择日期'){
      showToast('请填写实际结束日期')
      return
    }
  }

  if (fileList.value.length > 0){
    for (const temp of fileList.value) {
      form.append('Files',temp.file)
    }
  }
  const res = await api.Addproject(form)
  if (res.data.Ret === 1){
    if (isAudit.value){
      await audit(res.data.Msg)
    }else {
      showToast('新建成功')
      router.back()
    }
  }else {
    showToast(res.data.Msg)
  }
}

/**
 * 发布
 * @param id
 */
async function audit(id) {
  const params = {
    Token: getToken(),
    AuditType: 0,
    ProgressIds: [id],
  }
  const res = await api.Audit(params)
  if (res.data.Ret === 1) {
    showToast('提交成功!')
    router.back()
  }else {
    showToast(res.data.Msg)
  }
}

const id = ref('')
const tittle = ref('')
const route = useRoute()
const currentDate = ref([])
const isCanAdd = ref(false)
onMounted(()=>{
  currentDate.value = getCurrentDate()
  createTime.value = getCurrentTime().split(' ')[0]
  const au = Boolean(getAu())
  isCanAdd.value = au === true;
  id.value = route.query.id
  if (id.value){
    tittle.value = route.query.tittle
    // getDetails()
  }else {
    tittle.value = '新建填报'
  }
  unit.value.UnitType = 1
  planProgress.value = 0
  allProgress.value = 0
  type.value = '百分比%'
})
watch(fileList,async (value, oldValue)=>{
  console.log(value)
  console.log(oldValue)
})

</script>
<style scoped lang="scss">
.add-container{
  height: 100%;
  background-color: #F4F5F6;
  .header-container{
    display: flex;
    position: relative;
    align-items: center;
    justify-content: center;
    padding-top: 10px;
    padding-bottom: 10px;
    width: 100%;
    background-color: white;
    img{
      position: absolute;
      left: 0;
    }
    span:first-of-type{
      color: black;
    }
    span:last-child{
      position: absolute;
      right: 0;
      margin-right: 10px;
      font-weight: 500;
      color: #007AFF;
    }
  }
  .hide-overflow{
    overflow: hidden;
  }
  .form-container{
    padding-bottom: 40px;
    background-color: #F4F5F6;
    .form-item-item{
      border-radius: 4px;
      border: 0.5px solid #D8D8D8;
      .line-2{
        margin-left: 10px;
        margin-right: 10px;
        width: calc(100% - 20px);
        height: 1px;
        background: #D8D8D8;
      }
      .item{
        display: flex;
        align-items:center;
        height: 40px;
        span{
          font-size: 13px;
        }

        .bg-right{
          position: relative;
          height: 100%;
          span{
            display: inline-block;
            width: 80px;
            line-height: 40px;
            color: #a9a9a9;
            font-size: 13px;
          }
          input{
            width: 80px;
            line-height: 40px;
            background-color: transparent;
            outline-style: none ;
            border: none;
            font-size: 13px;
          }
        }
        .date-select{
          margin-left: auto;
          margin-right: 12px;
        }
      }

    }

    .form-item{
      margin: 10px 10px 10px 10px;
      display: flex;
      align-items:center;
      height: 40px;
      border-radius: 4px;
      border: 0.5px solid #D8D8D8;
      span{
        display: inline-block;
        width: 100px;
        margin-left: 10px;
        font-size: 14px;
      }
      .line{
        margin-left: 10px;
        width: 1px;
        height: 20px;
        background: #D8D8D8;
      }
      .bg-right{
        position: relative;
        height: 100%;
        width: 100%;
        span{
          width: 210px;
          /* 超出部分隐藏 */
          overflow: hidden;
          /* 不换行 */
          white-space: nowrap;
          /* 溢出用省略号代替 */
          text-overflow: ellipsis;
          line-height: 40px;
          color: #a9a9a9;
          margin-left: 20px;
          font-size: 15px;
        }
        input{
          line-height: 40px;
          margin-left: 20px;
          background-color: transparent;
          outline-style: none ;
          border: none;
          font-size: 15px;
        }
        .right-unit{
          margin-right: 6px;
          text-align: right;
        }
        .right-drop{
          margin-left: 6px;
        }
        .date-select{
          position: absolute;
          right: 0;
          top: 50%;
          margin-top: -10px;
          margin-right: 12px;
        }
      }

      .bg-img-right{
        display: flex;
        align-items: center;
        margin-right: 16px;
        width: 70%;
        height: 100%;
        border-radius: 6px;
        border: 1px solid #6C6C6C;
        img{
          margin-left: 12px;
        }
        span{
          width: 100%;
          line-height: 40px;
          color: #a9a9a9;
          font-size: 15px;
        }
      }
    }
    .upload-container{
      display: flex;
      flex-direction: column;
    }
    .func-wrapper{
      height: 50px;
      margin:12px;
      display: flex;
      span{
        font-size: 15px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
      }
      .save{
        margin-right: 2px;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
        width: 50%;
        color: #007AFF;
        background-color: white;
        border-radius: 4px;
        border: 1px solid #007AFF;
      }
      .add{
        margin-left: 2px;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
        width: 50%;
        color: #ffffff;
        border-radius: 4px;
        background-color: #007AFF;
      }

    }
  }

}

</style>
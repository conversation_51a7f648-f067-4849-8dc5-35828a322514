/**
 * 协作
 */
import { request } from '@/utils/request'
export default {
  GetIssueList:params => request({url:'/api/User/Issue/GetIssueList', params, isToken:false}),
  GetIssueStatus:params => request({url:'/api/User/Issue/GetIssueStatus', params, isToken:false}),
  GetIssueTypes:params => request({url:'/api/User/Issue/GetIssueTypes', params, isToken:false}),
  RemoveIssue:params => request({url:'/api/User/Issue/RemoveIssue', method: 'post', params, isToken:false}),
  AddIssue:params => request({url:'/api/User/Issue/AddIssue', method: 'post', params, isToken:false}),
  GetIssueDetail:params => request({url:'/api/User/Issue/GetIssueDetail', params, isToken:false}),
  RemoveIssueDocRel:params => request({url:'/api/User/Issue/RemoveIssueDocRel', method: 'post', params, isToken:false}),
  ModifyIssueStatus:params => request({url:'/api/User/Issue/ModifyIssueStatus', method: 'post', params, isToken:false}),
  ModifyIssueType:params => request({url:'/api/User/Issue/ModifyIssueType', method: 'post', params, isToken:false}),
  ModifyIssueEndDate:params => request({url:'/api/User/Issue/ModifyIssueEndDate', method: 'post', params, isToken:false}),
  AddIssueJoiner:params => request({url:'/api/User/Issue/AddIssueJoiner', method: 'post', params, isToken:false}),
  RemoveIssueJoiner:params => request({url:'/api/User/Issue/RemoveIssueJoiner', method: 'post', params, isToken:false}),
  AddComment:params => request({url:'/api/User/Issue/AddComment', method: 'post', params, isToken:false}),
  RemoveComment:params => request({url:'/api/User/Issue/RemoveComment', method: 'post', params, isToken:false})
}
<template>
  <div class="setting-container">
    <van-sticky>
      <van-nav-bar
          title="个人设置"
          left-text="返回"
          left-arrow
          @click-left="router.back()"
      />
    </van-sticky>
    <div class="card-wrapper">
      <van-image
          class="van-image"
          :src="require('../../assets/images/setting/card-bg.png')">

      </van-image>
      <span>{{name}}</span>
    </div>

    <div class="item-wrapper">
      <span class="label">账号</span>
      <span class="content"> {{account}}</span>
    </div>
    <div class="item-wrapper">
      <span class="label">邮箱/手机号</span>
      <span class="content"> {{email}}</span>
    </div>
    <div class="item-wrapper" @click="gotoModifyPassword">
      <span class="label">密码</span>
      <span class="content">修改密码</span>
    </div>
    <div class="item-wrapper">
      <span class="label">版本号</span>
      <span class="content"> {{version}}</span>
    </div>
    <div class="btn-wrapper">
      <van-button round block type="danger" @click="exit">
        退出登录
      </van-button>
    </div>
    <modify-password v-if="isShowPassword" @close="gotoModifyPassword"></modify-password>
  </div>


</template>
<script setup>
import {onMounted, ref} from "vue";
import router from "@/router";
import {getUserInfo} from "@/utils/user";
import ModifyPassword from "@/views/Setting/ModifyPassword.vue";
import {setIsAutoLogin} from "@/utils/isAutoLogin";

const name = ref('')
const account = ref('')
const email = ref('')
const version = ref('')
const isShowPassword = ref(false)

/**
 * 修改密码界面
 */
function gotoModifyPassword() {
  isShowPassword.value = !isShowPassword.value
}
function exit() {
  try {
    if (navigator.userAgent.indexOf('Android') > -1){
      mjs.toLogin()
    }else if (navigator.userAgent.indexOf('iPhone') > -1) {
      window.webkit.messageHandlers.toLogin.postMessage(null)
    }
  }catch(error){
    // 清空token
    window.sessionStorage.clear()
    setIsAutoLogin(false)
    // 跳转到登录页
    router.push('/')
  }
}
onMounted(()=>{
 const userInfo = JSON.parse(getUserInfo())
  name.value = userInfo.RealName
  account.value = userInfo.Account
  email.value = userInfo.Email ? userInfo.Email : userInfo.Mobile
  version.value = process.env.VUE_APP_VERSION // 这里可以替换为实际的版本号

})
</script>



<style scoped lang="scss">
.setting-container{
  height: 100%;
  background-color: #f5f7fb;
  .title-wrapper{
    font-size: 14px;
    font-weight: 500;
    color: #222222;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: white;
    height: 45px;
  }
  .card-wrapper{
    display: flex;
    position: relative;
    margin: 16px;
    .van-image{

    }
    span{
      font-size: 20px;
      font-weight: 500;
      color: white;
      position: absolute;
      top: 20px;
      left: 20px;
    }
  }

  .item-wrapper{
    padding-left: 10px;
    padding-right: 10px;
    border-radius: 8px;
    height: 50px;
    background-color: white;
    margin: 20px 16px 16px;
    display: flex;
    align-items: center;
    position: relative;
    .item-img{
      margin-left: 20px;
      margin-right: 20px;
    }
    .label{
      font-size: 14px;
      font-weight: 500;
    }
    .content{
      padding-right: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: black;
      font-size: 14px;
      font-weight: 500;
      right: 0;
      position: absolute;
    }
  }
  .btn-wrapper{
    margin: 16px;
  }
}

</style>
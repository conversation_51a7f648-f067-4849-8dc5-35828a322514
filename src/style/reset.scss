html, body, div, span, p, ul, li, h1, h2, h3, h4, h5, h6, img, audio, video, input, button {
  margin: 0;
  padding: 0;
  border: 0;
}

html, body {
  font-size: 14px;
  width: 100%;
  height: 100%;
  color: #283A4F;
  background-color: #FFFFFF;
  font-family: PingFangSC, PingFang SC, serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

ul {
  list-style: none;
}

a {
  text-decoration: none;
}

button {
  outline: none;
}

/* *号是css中的通配符，意思是所有的标签都有的属性，表示所有的标签都遵循的统一样式 */
* {
  box-sizing: border-box;
  font-family:"PingFang SC", "Helvetica Neue", Helvetica, "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
}

/* 设置滚动条的样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

/* 滚动槽 */
::-webkit-scrollbar-track {
  -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.3);
  border-radius: 10px;
}

/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: rgba(0, 0, 0, 0.3);
  -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.3);
}

/* 隐藏ul列表的滚动条*/
ul::-webkit-scrollbar {
  display: none;
}
